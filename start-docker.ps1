# SearXNG + MCP Docker启动脚本 (Windows版)
param(
    [switch]$Build,
    [switch]$NoBuild,
    [switch]$Down,
    [switch]$Logs,
    [switch]$Status,
    [string]$Service = "",
    [switch]$Help
)

function Show-Help {
    Write-Host "🐳 SearXNG + MCP Docker管理脚本" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\start-docker.ps1 [选项]" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -Build          强制重新构建Docker镜像" -ForegroundColor White
    Write-Host "  -NoBuild        跳过构建，直接启动" -ForegroundColor White
    Write-Host "  -Down           停止所有服务" -ForegroundColor White
    Write-Host "  -Logs           查看服务日志" -ForegroundColor White
    Write-Host "  -Status         查看服务状态" -ForegroundColor White
    Write-Host "  -Service <名称> 指定特定服务（配合-Logs使用）" -ForegroundColor White
    Write-Host "  -Help           显示此帮助信息" -ForegroundColor White
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\start-docker.ps1                    # 启动所有服务" -ForegroundColor Gray
    Write-Host "  .\start-docker.ps1 -Build             # 重新构建并启动" -ForegroundColor Gray
    Write-Host "  .\start-docker.ps1 -Logs              # 查看所有日志" -ForegroundColor Gray
    Write-Host "  .\start-docker.ps1 -Logs -Service redis # 查看Redis日志" -ForegroundColor Gray
    Write-Host "  .\start-docker.ps1 -Down              # 停止所有服务" -ForegroundColor Gray
    Write-Host "  .\start-docker.ps1 -Status            # 查看服务状态" -ForegroundColor Gray
}

if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🐳 SearXNG + MCP Docker管理脚本" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 检查Docker是否运行
Write-Host "🔍 检查Docker环境..." -ForegroundColor Blue
try {
    $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker运行正常 (版本: $dockerVersion)" -ForegroundColor Green
    } else {
        throw "Docker命令执行失败"
    }
} catch {
    Write-Host "❌ Docker未运行或未安装" -ForegroundColor Red
    Write-Host "请确保Docker Desktop已启动并正常运行" -ForegroundColor Yellow
    Write-Host "下载地址: https://www.docker.com/products/docker-desktop" -ForegroundColor Blue
    exit 1
}

# 检查docker-compose文件
$composeFile = "docker-compose.windows.yml"
if (-not (Test-Path $composeFile)) {
    Write-Host "❌ $composeFile 文件不存在" -ForegroundColor Red
    exit 1
}

# 创建必要的目录
Write-Host "📁 创建必要的目录..." -ForegroundColor Blue
$directories = @("logs", "enhanced_search_mcp\logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ 创建目录: $dir" -ForegroundColor Green
    }
}

# 停止服务
if ($Down) {
    Write-Host "🛑 停止所有服务..." -ForegroundColor Yellow
    docker-compose -f $composeFile down --remove-orphans
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 服务已停止" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 停止服务时出现警告" -ForegroundColor Yellow
    }
    exit 0
}

# 查看服务状态
if ($Status) {
    Write-Host "📊 服务状态:" -ForegroundColor Blue
    docker-compose -f $composeFile ps
    exit 0
}

# 查看日志
if ($Logs) {
    if ($Service) {
        Write-Host "📋 查看 $Service 服务日志..." -ForegroundColor Blue
        docker-compose -f $composeFile logs -f $Service
    } else {
        Write-Host "📋 查看所有服务日志..." -ForegroundColor Blue
        docker-compose -f $composeFile logs -f
    }
    exit 0
}

# 构建镜像
if ($Build -or -not $NoBuild) {
    Write-Host "🔨 构建Docker镜像..." -ForegroundColor Blue
    docker-compose -f $composeFile build --no-cache
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker镜像构建失败" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Docker镜像构建成功" -ForegroundColor Green
}

# 启动服务
Write-Host "🚀 启动服务..." -ForegroundColor Blue
docker-compose -f $composeFile up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 服务启动失败" -ForegroundColor Red
    Write-Host "请检查Docker日志获取详细信息" -ForegroundColor Yellow
    exit 1
}

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "`n📊 服务状态:" -ForegroundColor Cyan
docker-compose -f $composeFile ps

# 检查服务健康状态
Write-Host "`n🔍 检查服务健康状态..." -ForegroundColor Blue

# 检查Redis
try {
    $redisContainer = docker ps --filter "name=searxng-redis" --format "{{.Names}}"
    if ($redisContainer) {
        Write-Host "✅ Redis服务正常运行" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Redis服务检查失败" -ForegroundColor Yellow
}

# 检查SearXNG
$maxRetries = 6
$retryCount = 0
$searxngHealthy = $false

while ($retryCount -lt $maxRetries -and -not $searxngHealthy) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8885" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ SearXNG服务正常 (http://localhost:8885)" -ForegroundColor Green
            $searxngHealthy = $true
        }
    } catch {
        $retryCount++
        if ($retryCount -lt $maxRetries) {
            Write-Host "⏳ SearXNG启动中... (尝试 $retryCount/$maxRetries)" -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        } else {
            Write-Host "⚠️ SearXNG服务检查失败，可能仍在启动中" -ForegroundColor Yellow
        }
    }
}

# 检查MCP服务器
$mcpHealthy = $false
$retryCount = 0

while ($retryCount -lt $maxRetries -and -not $mcpHealthy) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8881/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ MCP服务器正常 (http://localhost:8881)" -ForegroundColor Green
            $mcpHealthy = $true
        }
    } catch {
        $retryCount++
        if ($retryCount -lt $maxRetries) {
            Write-Host "⏳ MCP服务器启动中... (尝试 $retryCount/$maxRetries)" -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        } else {
            Write-Host "⚠️ MCP服务器检查失败，可能仍在启动中" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n🎉 Docker服务启动完成！" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Cyan
Write-Host "📡 SearXNG Web界面: http://localhost:8885" -ForegroundColor Blue
Write-Host "🤖 MCP服务器: http://localhost:8881" -ForegroundColor Blue
Write-Host "🔍 搜索API: http://localhost:8881/search" -ForegroundColor Blue
Write-Host "💚 健康检查: http://localhost:8881/health" -ForegroundColor Blue
Write-Host "🗄️ Redis缓存: localhost:6379" -ForegroundColor Blue

Write-Host "`n📋 常用命令:" -ForegroundColor Yellow
Write-Host "  查看日志: .\start-docker.ps1 -Logs" -ForegroundColor Gray
Write-Host "  查看状态: .\start-docker.ps1 -Status" -ForegroundColor Gray
Write-Host "  停止服务: .\start-docker.ps1 -Down" -ForegroundColor Gray
Write-Host "  重新构建: .\start-docker.ps1 -Build" -ForegroundColor Gray
Write-Host "  获取帮助: .\start-docker.ps1 -Help" -ForegroundColor Gray

Write-Host "`n💡 提示:" -ForegroundColor Yellow
Write-Host "  - 如果服务启动失败，请使用 -Logs 查看详细日志" -ForegroundColor Gray
Write-Host "  - 首次启动可能需要较长时间下载依赖" -ForegroundColor Gray
Write-Host "  - 确保端口 8885 和 8881 未被其他程序占用" -ForegroundColor Gray
