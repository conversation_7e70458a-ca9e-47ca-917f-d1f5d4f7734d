# SearXNG依赖 - Windows兼容版本
# 排除了uvloop和setproctitle等Linux专用包

# 核心依赖
pyyaml>=6.0
babel>=2.12.0
flask>=2.3.0
jinja2>=3.1.0
lxml>=4.9.0
certifi>=2023.0.0
requests>=2.31.0
urllib3>=2.0.0
markupsafe>=2.1.0
python-dateutil>=2.8.0

# HTTP客户端
httpx>=0.25.0
httpx[http2]>=0.25.0

# 压缩和编码
brotli>=1.0.0

# 缓存
redis>=5.0.0

# 文本处理
markdown-it-py>=3.0.0
pygments>=2.19.0

# 数据序列化
msgspec>=0.19.0

# 日期处理
isodate>=0.7.0

# 配置文件处理
tomli>=2.2.0

# Flask扩展
flask-babel>=4.0.0
werkzeug>=3.0.0

# 异步支持 (Windows兼容)
asyncio-compat>=0.1.0

# 网络代理支持
python-socks>=2.0.0

# 文本预测 (可选)
# fasttext-predict>=0.9.0  # 可能在Windows上有问题，先注释

# 命令行工具
typer>=0.9.0
