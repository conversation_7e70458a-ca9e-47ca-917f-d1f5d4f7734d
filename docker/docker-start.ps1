# Windows Docker启动脚本
param(
    [switch]$Build,
    [switch]$NoBuild,
    [switch]$Down,
    [switch]$Logs,
    [string]$Service = ""
)

Write-Host "🐳 SearXNG + MCP Docker管理脚本" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 检查Docker是否运行
try {
    docker version | Out-Null
    Write-Host "✅ Docker运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker未运行或未安装" -ForegroundColor Red
    Write-Host "请确保Docker Desktop已启动" -ForegroundColor Yellow
    exit 1
}

# 检查docker-compose文件
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml文件不存在" -ForegroundColor Red
    exit 1
}

# 停止服务
if ($Down) {
    Write-Host "🛑 停止所有服务..." -ForegroundColor Yellow
    docker-compose down
    Write-Host "✅ 服务已停止" -ForegroundColor Green
    exit 0
}

# 查看日志
if ($Logs) {
    if ($Service) {
        Write-Host "📋 查看 $Service 服务日志..." -ForegroundColor Blue
        docker-compose logs -f $Service
    } else {
        Write-Host "📋 查看所有服务日志..." -ForegroundColor Blue
        docker-compose logs -f
    }
    exit 0
}

# 构建并启动服务
if ($Build -or -not $NoBuild) {
    Write-Host "🔨 构建Docker镜像..." -ForegroundColor Blue
    docker-compose build
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker镜像构建失败" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Docker镜像构建成功" -ForegroundColor Green
}

# 启动服务
Write-Host "🚀 启动服务..." -ForegroundColor Blue
docker-compose up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 服务启动失败" -ForegroundColor Red
    exit 1
}

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务状态
Write-Host "`n📊 服务状态:" -ForegroundColor Cyan
docker-compose ps

# 检查服务健康状态
Write-Host "`n🔍 检查服务健康状态..." -ForegroundColor Blue

# 检查SearXNG
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8885/healthz" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SearXNG服务正常 (http://localhost:8885)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ SearXNG服务检查失败" -ForegroundColor Yellow
}

# 检查MCP服务器
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8881/health" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ MCP服务器正常 (http://localhost:8881)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ MCP服务器检查失败" -ForegroundColor Yellow
}

Write-Host "`n🎉 服务启动完成！" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Cyan
Write-Host "📡 SearXNG Web界面: http://localhost:8885" -ForegroundColor Blue
Write-Host "🤖 MCP服务器: http://localhost:8881" -ForegroundColor Blue
Write-Host "🔍 搜索API: http://localhost:8881/search" -ForegroundColor Blue
Write-Host "💚 健康检查: http://localhost:8881/health" -ForegroundColor Blue
Write-Host "`n📋 常用命令:" -ForegroundColor Yellow
Write-Host "  查看日志: .\docker\docker-start.ps1 -Logs" -ForegroundColor Gray
Write-Host "  停止服务: .\docker\docker-start.ps1 -Down" -ForegroundColor Gray
Write-Host "  重新构建: .\docker\docker-start.ps1 -Build" -ForegroundColor Gray
