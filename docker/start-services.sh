#!/bin/bash

# SearXNG + MCP服务器启动脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 启动SearXNG + MCP服务器 Docker容器${NC}"
echo -e "${CYAN}===========================================${NC}"

# 设置默认环境变量
export SEARXNG_PORT=${SEARXNG_PORT:-8885}
export SEARXNG_BIND_ADDRESS=${SEARXNG_BIND_ADDRESS:-0.0.0.0}
export MCP_HOST=${MCP_HOST:-0.0.0.0}
export MCP_PORT=${MCP_PORT:-8881}
export SEARXNG_URL=${SEARXNG_URL:-http://localhost:8885}
export LOG_LEVEL=${LOG_LEVEL:-INFO}

# 创建日志目录
mkdir -p /app/logs /app/enhanced_search_mcp/logs

# 等待Redis启动（如果启用）
if [ "${ENABLE_CACHE}" = "true" ] && [ -n "${REDIS_URL}" ]; then
    echo -e "${YELLOW}⏳ 等待Redis服务启动...${NC}"
    timeout=30
    while [ $timeout -gt 0 ]; do
        if redis-cli -u "${REDIS_URL}" ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Redis连接成功${NC}"
            break
        fi
        sleep 1
        timeout=$((timeout - 1))
    done
    
    if [ $timeout -eq 0 ]; then
        echo -e "${YELLOW}⚠️ Redis连接超时，将禁用缓存功能${NC}"
        export ENABLE_CACHE=false
    fi
fi

# 启动SearXNG的函数
start_searxng() {
    echo -e "${PURPLE}🔍 启动SearXNG搜索引擎...${NC}"
    cd /app
    
    # 检查配置文件
    if [ ! -f "/app/searx/settings.yml" ]; then
        echo -e "${RED}❌ SearXNG配置文件不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ SearXNG配置文件检查通过${NC}"
    echo -e "${BLUE}📡 SearXNG将在端口 ${SEARXNG_PORT} 启动${NC}"
    
    # 启动SearXNG (后台运行)
    python -m searx.webapp > /app/logs/searxng.log 2>&1 &
    SEARXNG_PID=$!
    echo -e "${GREEN}✅ SearXNG已启动 (PID: $SEARXNG_PID)${NC}"
    
    # 等待SearXNG启动
    echo -e "${YELLOW}⏳ 等待SearXNG启动...${NC}"
    for i in {1..30}; do
        if curl -s --connect-timeout 2 http://localhost:${SEARXNG_PORT}/healthz > /dev/null 2>&1; then
            echo -e "${GREEN}✅ SearXNG启动成功${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${RED}❌ SearXNG启动超时${NC}"
    return 1
}

# 启动MCP服务器的函数
start_mcp_server() {
    echo -e "${PURPLE}🤖 启动Enhanced Search MCP服务器...${NC}"
    cd /app/enhanced_search_mcp
    
    # 检查MCP服务器文件
    if [ ! -f "server.py" ]; then
        echo -e "${RED}❌ MCP服务器文件不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ MCP服务器文件检查通过${NC}"
    echo -e "${BLUE}📡 MCP服务器将在端口 ${MCP_PORT} 启动${NC}"
    
    # 启动MCP服务器 (后台运行)
    python server.py > /app/enhanced_search_mcp/logs/mcp_server.log 2>&1 &
    MCP_PID=$!
    echo -e "${GREEN}✅ MCP服务器已启动 (PID: $MCP_PID)${NC}"
    
    # 等待MCP服务器启动
    echo -e "${YELLOW}⏳ 等待MCP服务器启动...${NC}"
    for i in {1..30}; do
        if curl -s --connect-timeout 2 http://localhost:${MCP_PORT}/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ MCP服务器启动成功${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${RED}❌ MCP服务器启动超时${NC}"
    return 1
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止服务...${NC}"
    
    if [ ! -z "$SEARXNG_PID" ]; then
        kill $SEARXNG_PID 2>/dev/null || true
        echo -e "${GREEN}✅ SearXNG已停止${NC}"
    fi
    
    if [ ! -z "$MCP_PID" ]; then
        kill $MCP_PID 2>/dev/null || true
        echo -e "${GREEN}✅ MCP服务器已停止${NC}"
    fi
    
    echo -e "${CYAN}👋 服务已全部停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主启动流程
echo -e "${CYAN}🚀 开始启动服务...${NC}"

# 启动SearXNG
if start_searxng; then
    echo -e "${GREEN}✅ SearXNG启动完成${NC}"
else
    echo -e "${RED}❌ SearXNG启动失败${NC}"
    cleanup
    exit 1
fi

# 启动MCP服务器
if start_mcp_server; then
    echo -e "${GREEN}✅ MCP服务器启动完成${NC}"
else
    echo -e "${RED}❌ MCP服务器启动失败${NC}"
    cleanup
    exit 1
fi

# 显示服务信息
echo -e "\n${CYAN}🎉 所有服务启动完成！${NC}"
echo -e "${CYAN}========================${NC}"
echo -e "${GREEN}📡 SearXNG Web界面: http://localhost:${SEARXNG_PORT}${NC}"
echo -e "${GREEN}🤖 MCP服务器: http://localhost:${MCP_PORT}${NC}"
echo -e "${GREEN}🔍 搜索API: http://localhost:${MCP_PORT}/search${NC}"
echo -e "${GREEN}💚 健康检查: http://localhost:${MCP_PORT}/health${NC}"
echo -e "${YELLOW}📋 按 Ctrl+C 停止所有服务${NC}"

# 保持容器运行
wait
