# SearXNG + MCP Server Docker镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN useradd -m -u 1000 searxng && \
    chown -R searxng:searxng /app

# 复制requirements文件
COPY requirements.txt /app/
COPY enhanced_search_mcp/requirements.txt /app/mcp_requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r mcp_requirements.txt

# 复制SearXNG源码
COPY searx/ /app/searx/
COPY searxng_extra/ /app/searxng_extra/

# 复制MCP服务器源码
COPY enhanced_search_mcp/ /app/enhanced_search_mcp/

# 复制启动脚本
COPY docker/ /app/docker/

# 设置权限
RUN chown -R searxng:searxng /app && \
    chmod +x /app/docker/*.sh

# 切换到非root用户
USER searxng

# 创建日志目录
RUN mkdir -p /app/logs /app/enhanced_search_mcp/logs

# 环境变量
ENV PYTHONPATH=/app
ENV SEARXNG_SETTINGS_PATH=/app/searx/settings.yml
ENV SEARXNG_PORT=8885
ENV SEARXNG_BIND_ADDRESS=0.0.0.0
ENV MCP_HOST=0.0.0.0
ENV MCP_PORT=8881
ENV SEARXNG_URL=http://localhost:8885

# 暴露端口
EXPOSE 8885 8881

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8885/healthz || exit 1

# 启动脚本
CMD ["/app/docker/start-services.sh"]
