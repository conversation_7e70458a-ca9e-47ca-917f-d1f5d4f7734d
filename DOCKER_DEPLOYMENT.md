# SearXNG + MCP Server Docker部署指南

## 概述

本项目提供了完整的Docker化解决方案，让您可以在Windows环境中轻松部署SearXNG搜索引擎和Enhanced Search MCP服务器。

## 系统要求

### Windows环境
- Windows 10/11 (推荐)
- Docker Desktop for Windows
- PowerShell 5.1+ 或 PowerShell Core 7+
- 至少 4GB 可用内存
- 至少 2GB 可用磁盘空间

### 端口要求
- **8885**: SearXNG Web界面
- **8881**: MCP服务器API
- **6379**: Redis缓存服务

## 快速开始

### 1. 安装Docker Desktop

如果尚未安装Docker Desktop：

1. 下载：https://www.docker.com/products/docker-desktop
2. 安装并启动Docker Desktop
3. 确保Docker Desktop正在运行

### 2. 启动服务

在项目根目录打开PowerShell，运行：

```powershell
# 首次启动（会自动构建镜像）
.\start-docker.ps1

# 或者强制重新构建
.\start-docker.ps1 -Build
```

### 3. 验证部署

启动完成后，访问以下地址验证服务：

- **SearXNG Web界面**: http://localhost:8885
- **MCP服务器健康检查**: http://localhost:8881/health
- **搜索API测试**: http://localhost:8881/search?query=test

## 管理命令

### 启动服务
```powershell
# 正常启动
.\start-docker.ps1

# 跳过构建直接启动
.\start-docker.ps1 -NoBuild

# 强制重新构建
.\start-docker.ps1 -Build
```

### 查看状态和日志
```powershell
# 查看服务状态
.\start-docker.ps1 -Status

# 查看所有服务日志
.\start-docker.ps1 -Logs

# 查看特定服务日志
.\start-docker.ps1 -Logs -Service searxng-mcp
.\start-docker.ps1 -Logs -Service redis
```

### 停止服务
```powershell
# 停止所有服务
.\start-docker.ps1 -Down
```

### 获取帮助
```powershell
# 显示帮助信息
.\start-docker.ps1 -Help
```

## 服务架构

### 容器组成
1. **searxng-mcp-server**: 主服务容器
   - SearXNG搜索引擎 (端口8885)
   - Enhanced Search MCP服务器 (端口8881)
   
2. **searxng-redis**: Redis缓存服务
   - 提供搜索结果缓存
   - 提高搜索性能

### 网络配置
- 所有服务运行在 `searxng-network` 网络中
- 服务间通过容器名称通信
- 对外暴露必要端口

## 配置说明

### 环境变量配置

主要配置项在 `docker-compose.windows.yml` 中：

```yaml
environment:
  # SearXNG配置
  - SEARXNG_PORT=8885
  - SEARXNG_BIND_ADDRESS=0.0.0.0
  - SEARXNG_SECRET=your-secret-key-change-this  # 请修改此密钥
  
  # MCP服务器配置
  - MCP_HOST=0.0.0.0
  - MCP_PORT=8881
  - LOG_LEVEL=INFO
  
  # 搜索配置
  - MAX_CONCURRENT_SEARCHES=5
  - DEFAULT_ENGINES=google,bing,brave
  - MAX_RESULTS_PER_QUERY=10
```

### 自定义配置

1. **修改SearXNG配置**：
   - 编辑 `searx/settings.yml`
   - 重启容器生效

2. **修改MCP服务器配置**：
   - 编辑 `enhanced_search_mcp/config.py`
   - 或通过环境变量覆盖

3. **修改Docker配置**：
   - 编辑 `docker-compose.windows.yml`
   - 重新构建并启动

## 数据持久化

### 日志文件
- SearXNG日志: `./logs/searxng.log`
- MCP服务器日志: `./enhanced_search_mcp/logs/mcp_server.log`

### Redis数据
- Redis数据存储在Docker卷 `redis_data` 中
- 容器重启后数据保持

## 故障排除

### 常见问题

1. **端口被占用**
   ```powershell
   # 检查端口占用
   netstat -ano | findstr :8885
   netstat -ano | findstr :8881
   
   # 终止占用进程
   taskkill /PID <进程ID> /F
   ```

2. **Docker构建失败**
   ```powershell
   # 清理Docker缓存
   docker system prune -a
   
   # 重新构建
   .\start-docker.ps1 -Build
   ```

3. **服务启动失败**
   ```powershell
   # 查看详细日志
   .\start-docker.ps1 -Logs
   
   # 检查容器状态
   docker ps -a
   ```

4. **内存不足**
   - 增加Docker Desktop内存限制
   - 关闭其他不必要的应用程序

### 调试模式

启用详细日志：

```powershell
# 修改docker-compose.windows.yml中的LOG_LEVEL
- LOG_LEVEL=DEBUG
```

然后重新启动服务。

## 性能优化

### 资源配置
- 推荐分配至少2GB内存给Docker
- SSD存储可提高性能
- 启用Redis缓存减少重复搜索

### 搜索优化
- 调整 `MAX_CONCURRENT_SEARCHES` 控制并发数
- 配置 `CACHE_TTL` 设置缓存时间
- 选择合适的搜索引擎组合

## 安全注意事项

1. **修改默认密钥**：
   - 更改 `SEARXNG_SECRET` 环境变量
   
2. **网络安全**：
   - 仅在可信网络中暴露端口
   - 考虑使用反向代理

3. **数据隐私**：
   - SearXNG不记录搜索历史
   - 可配置更严格的隐私设置

## 更新和维护

### 更新镜像
```powershell
# 拉取最新基础镜像
docker pull python:3.11-slim
docker pull redis:7-alpine

# 重新构建
.\start-docker.ps1 -Build
```

### 备份配置
定期备份以下文件：
- `searx/settings.yml`
- `enhanced_search_mcp/config.py`
- `docker-compose.windows.yml`

## 支持和反馈

如果遇到问题：
1. 查看日志文件获取详细错误信息
2. 检查Docker Desktop状态
3. 确认系统资源充足
4. 参考故障排除部分

## 许可证

本项目遵循原SearXNG项目的AGPL-3.0许可证。
