#!/usr/bin/env python3
"""
SearXNG直接适配器 - 直接调用SearXNG Python模块，无需HTTP API
解决端口冲突问题，提供更高性能的搜索体验
"""

import asyncio
import logging
import os
import sys
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

# 添加SearXNG路径到Python路径
searxng_root = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, searxng_root)

try:
    import searx.search
    import searx.settings
    import searx.engines
    from searx.search.models import SearchQuery, EngineRef
    from searx.search import Search
    SEARXNG_AVAILABLE = True
    # 导入类型用于类型注解
    SearchQueryType = SearchQuery
    EngineRefType = EngineRef
except ImportError as e:
    SEARXNG_AVAILABLE = False
    IMPORT_ERROR = str(e)
    # 创建占位符类型
    class SearchQueryType:
        pass
    class EngineRefType:
        pass

logger = logging.getLogger(__name__)

class SearXNGDirectAdapter:
    """SearXNG直接适配器 - 直接调用Python模块"""

    def __init__(self, timeout: int = 30, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.initialized = False

        # 默认搜索引擎配置
        self.default_engines = ["google", "bing", "brave", "duckduckgo"]
        self.categories = "general"

        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'cache_hits': 0
        }

        # 检查SearXNG可用性
        if not SEARXNG_AVAILABLE:
            logger.error(f"SearXNG模块不可用: {IMPORT_ERROR}")
            raise ImportError(f"无法导入SearXNG模块: {IMPORT_ERROR}")

    async def initialize(self):
        """初始化SearXNG引擎"""
        if self.initialized:
            return

        try:
            logger.info("初始化SearXNG引擎...")

            # 在异步环境中运行同步初始化代码
            await asyncio.get_event_loop().run_in_executor(
                None, self._sync_initialize
            )

            self.initialized = True
            logger.info("SearXNG引擎初始化完成")

        except Exception as e:
            logger.error(f"SearXNG引擎初始化失败: {e}")
            raise

    def _sync_initialize(self):
        """同步初始化SearXNG引擎"""
        try:
            # 加载引擎配置
            settings_engines = searx.settings['engines']

            # 初始化引擎
            searx.search.load_engines(settings_engines)

            # 初始化网络配置
            searx.search.initialize_network(settings_engines, searx.settings['outgoing'])
            searx.search.check_network_configuration()

            # 初始化指标和处理器
            searx.search.initialize_metrics([engine['name'] for engine in settings_engines])
            searx.search.initialize_processors(settings_engines)

            logger.info("SearXNG同步初始化完成")

        except Exception as e:
            logger.error(f"SearXNG同步初始化失败: {e}")
            raise

    async def search(
        self,
        query: str,
        engines: Optional[List[str]] = None,
        categories: Optional[str] = None,
        language: str = "zh-CN",
        time_range: Optional[str] = None,
        safesearch: int = 1,
        pageno: int = 1
    ) -> Dict[str, Any]:
        """
        执行搜索查询

        Args:
            query: 搜索查询
            engines: 搜索引擎列表
            categories: 搜索类别
            language: 搜索语言
            time_range: 时间范围
            safesearch: 安全搜索级别
            pageno: 页码

        Returns:
            搜索结果字典
        """
        start_time = time.time()
        self.stats['total_requests'] += 1

        try:
            # 确保已初始化
            if not self.initialized:
                await self.initialize()

            # 准备引擎引用列表
            engine_refs = self._prepare_engine_refs(engines or self.default_engines)

            # 创建搜索查询对象
            search_query = SearchQuery(
                query=query,
                engineref_list=engine_refs,
                lang=language,
                safesearch=safesearch,
                pageno=pageno,
                time_range=time_range,
                timeout_limit=self.timeout
            )

            # 在线程池中执行搜索
            result_container = await asyncio.get_event_loop().run_in_executor(
                None, self._sync_search, search_query
            )

            # 转换结果格式
            result_dict = self._convert_result_container(result_container, search_query)

            # 更新统计信息
            response_time = time.time() - start_time
            self.stats['successful_requests'] += 1
            self.stats['total_response_time'] += response_time

            logger.info(f"搜索完成: '{query}' ({response_time:.2f}秒, {len(result_dict.get('results', []))}个结果)")

            return result_dict

        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"搜索失败: {e}")
            raise

    def _sync_search(self, search_query: SearchQueryType):
        """同步执行搜索"""
        try:
            search = Search(search_query)
            return search.search()
        except Exception as e:
            logger.error(f"同步搜索失败: {e}")
            raise

    def _prepare_engine_refs(self, engines: List[str]) -> List[EngineRefType]:
        """准备引擎引用列表"""
        engine_refs = []

        for engine_name in engines:
            try:
                # 检查引擎是否可用
                if engine_name in searx.engines.engines:
                    if SEARXNG_AVAILABLE:
                        engine_refs.append(EngineRef(engine_name, "general"))
                    else:
                        logger.warning("SearXNG不可用，无法创建引擎引用")
                else:
                    logger.warning(f"引擎 '{engine_name}' 不可用，跳过")
            except Exception as e:
                logger.warning(f"准备引擎 '{engine_name}' 失败: {e}")

        if not engine_refs:
            # 如果没有可用引擎，使用默认引擎
            logger.warning("没有可用引擎，使用默认引擎")
            for engine_name in ["google", "bing", "duckduckgo"]:
                if engine_name in searx.engines.engines and SEARXNG_AVAILABLE:
                    engine_refs.append(EngineRef(engine_name, "general"))
                    break

        return engine_refs

    def _convert_result_container(self, result_container, search_query: SearchQueryType) -> Dict[str, Any]:
        """转换结果容器为字典格式"""
        try:
            # 获取有序结果
            results = result_container.get_ordered_results()

            # 转换结果格式
            converted_results = []
            for result in results:
                converted_result = {
                    'title': getattr(result, 'title', ''),
                    'content': getattr(result, 'content', ''),
                    'url': getattr(result, 'url', ''),
                    'engine': getattr(result, 'engine', ''),
                    'score': getattr(result, 'score', 0),
                    'category': getattr(result, 'category', 'general'),
                    'publishedDate': self._format_date(getattr(result, 'publishedDate', None)),
                }
                converted_results.append(converted_result)

            # 构建完整结果字典
            result_dict = {
                "search": {
                    "q": search_query.query,
                    "pageno": search_query.pageno,
                    "lang": search_query.lang,
                    "safesearch": search_query.safesearch,
                    "timerange": search_query.time_range,
                },
                "results": converted_results,
                "infoboxes": list(result_container.infoboxes) if hasattr(result_container, 'infoboxes') else [],
                "suggestions": list(result_container.suggestions) if hasattr(result_container, 'suggestions') else [],
                "answers": list(result_container.answers) if hasattr(result_container, 'answers') else [],
                "paging": getattr(result_container, 'paging', {}),
                "number_of_results": getattr(result_container, 'number_of_results', len(converted_results)),
            }

            return result_dict

        except Exception as e:
            logger.error(f"转换结果失败: {e}")
            raise

    def _format_date(self, date_obj) -> Optional[str]:
        """格式化日期对象"""
        if date_obj is None:
            return None

        try:
            if isinstance(date_obj, datetime):
                return date_obj.isoformat()
            elif isinstance(date_obj, str):
                return date_obj
            else:
                return str(date_obj)
        except Exception:
            return None

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.initialized:
                await self.initialize()

            # 执行简单搜索测试
            test_result = await self.search("test", engines=["duckduckgo"])

            return {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'initialized': self.initialized,
                'available_engines': len(searx.engines.engines) if SEARXNG_AVAILABLE else 0,
                'test_results': len(test_result.get('results', [])),
                'stats': self.get_stats()
            }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'initialized': self.initialized
            }

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = max(self.stats['total_requests'], 1)
        avg_response_time = self.stats['total_response_time'] / max(self.stats['successful_requests'], 1)
        success_rate = self.stats['successful_requests'] / total_requests

        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'cache_hits': self.stats['cache_hits']
        }

    async def close(self):
        """清理资源"""
        logger.info("SearXNG直接适配器关闭")
        # 直接适配器不需要特殊清理
