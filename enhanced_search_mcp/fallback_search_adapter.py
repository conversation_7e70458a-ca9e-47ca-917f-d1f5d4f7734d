#!/usr/bin/env python3
"""
备用搜索适配器 - 当SearXNG不可用时使用
直接调用各种搜索引擎API，兼容Windows环境
"""

import asyncio
import logging
import os
import time
import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp
from urllib.parse import quote_plus, urljoin

logger = logging.getLogger(__name__)

class FallbackSearchAdapter:
    """备用搜索适配器 - 直接调用搜索引擎API"""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.initialized = False
        
        # 支持的搜索引擎
        self.engines = {
            "duckduckgo": self._search_duckduckgo,
            "bing": self._search_bing,
            "google": self._search_google_custom,
            "brave": self._search_brave,
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'cache_hits': 0
        }
        
        # HTTP会话
        self.session = None
    
    async def initialize(self):
        """初始化HTTP会话"""
        if self.initialized:
            return
        
        try:
            logger.info("初始化备用搜索适配器...")
            
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json, text/html, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            )
            
            self.initialized = True
            logger.info("备用搜索适配器初始化完成")
            
        except Exception as e:
            logger.error(f"备用搜索适配器初始化失败: {e}")
            raise
    
    async def search(
        self, 
        query: str, 
        engines: Optional[List[str]] = None,
        categories: Optional[str] = None,
        language: str = "zh-CN",
        time_range: Optional[str] = None,
        safesearch: int = 1,
        pageno: int = 1
    ) -> Dict[str, Any]:
        """
        执行搜索查询
        
        Args:
            query: 搜索查询
            engines: 搜索引擎列表
            categories: 搜索类别
            language: 搜索语言
            time_range: 时间范围
            safesearch: 安全搜索级别
            pageno: 页码
        
        Returns:
            搜索结果字典
        """
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        try:
            # 确保已初始化
            if not self.initialized:
                await self.initialize()
            
            # 选择搜索引擎
            if not engines:
                engines = ["duckduckgo", "bing"]
            
            # 并发搜索多个引擎
            tasks = []
            for engine in engines:
                if engine in self.engines:
                    task = self._search_with_engine(engine, query, language, safesearch, pageno)
                    tasks.append(task)
            
            if not tasks:
                raise Exception("没有可用的搜索引擎")
            
            # 执行并发搜索
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 合并结果
            merged_results = self._merge_results(results, query)
            
            # 更新统计信息
            response_time = time.time() - start_time
            self.stats['successful_requests'] += 1
            self.stats['total_response_time'] += response_time
            
            logger.info(f"备用搜索完成: '{query}' ({response_time:.2f}秒, {len(merged_results.get('results', []))}个结果)")
            
            return merged_results
                    
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"备用搜索失败: {e}")
            
            # 返回空结果而不是抛出异常
            return self._empty_result(query)
    
    async def _search_with_engine(self, engine: str, query: str, language: str, safesearch: int, pageno: int):
        """使用指定引擎搜索"""
        try:
            search_func = self.engines[engine]
            return await search_func(query, language, safesearch, pageno)
        except Exception as e:
            logger.warning(f"引擎 {engine} 搜索失败: {e}")
            return []
    
    async def _search_duckduckgo(self, query: str, language: str, safesearch: int, pageno: int) -> List[Dict]:
        """DuckDuckGo搜索"""
        try:
            # DuckDuckGo Instant Answer API
            url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    # 处理抽象结果
                    if data.get('Abstract'):
                        results.append({
                            'title': data.get('Heading', query),
                            'content': data.get('Abstract', ''),
                            'url': data.get('AbstractURL', ''),
                            'engine': 'duckduckgo',
                            'score': 8.0,
                            'category': 'general'
                        })
                    
                    # 处理相关主题
                    for topic in data.get('RelatedTopics', [])[:3]:
                        if isinstance(topic, dict) and topic.get('Text'):
                            results.append({
                                'title': topic.get('Text', '')[:100],
                                'content': topic.get('Text', ''),
                                'url': topic.get('FirstURL', ''),
                                'engine': 'duckduckgo',
                                'score': 6.0,
                                'category': 'general'
                            })
                    
                    return results
                    
        except Exception as e:
            logger.warning(f"DuckDuckGo搜索失败: {e}")
        
        return []
    
    async def _search_bing(self, query: str, language: str, safesearch: int, pageno: int) -> List[Dict]:
        """Bing搜索（通过网页抓取）"""
        try:
            # 使用Bing搜索页面
            url = "https://www.bing.com/search"
            params = {
                'q': query,
                'count': 10,
                'offset': (pageno - 1) * 10,
                'mkt': 'zh-CN' if language.startswith('zh') else 'en-US'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    html = await response.text()
                    return self._parse_bing_results(html)
                    
        except Exception as e:
            logger.warning(f"Bing搜索失败: {e}")
        
        return []
    
    async def _search_google_custom(self, query: str, language: str, safesearch: int, pageno: int) -> List[Dict]:
        """Google自定义搜索（需要API密钥）"""
        try:
            # 如果有Google API密钥，使用Custom Search API
            api_key = os.getenv('GOOGLE_API_KEY')
            search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
            
            if api_key and search_engine_id:
                url = "https://www.googleapis.com/customsearch/v1"
                params = {
                    'key': api_key,
                    'cx': search_engine_id,
                    'q': query,
                    'num': 10,
                    'start': (pageno - 1) * 10 + 1,
                    'lr': f'lang_{language[:2]}' if language else 'lang_zh'
                }
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        for item in data.get('items', []):
                            results.append({
                                'title': item.get('title', ''),
                                'content': item.get('snippet', ''),
                                'url': item.get('link', ''),
                                'engine': 'google',
                                'score': 7.0,
                                'category': 'general'
                            })
                        
                        return results
                        
        except Exception as e:
            logger.warning(f"Google搜索失败: {e}")
        
        return []
    
    async def _search_brave(self, query: str, language: str, safesearch: int, pageno: int) -> List[Dict]:
        """Brave搜索"""
        try:
            # Brave搜索API（如果有API密钥）
            api_key = os.getenv('BRAVE_API_KEY')
            
            if api_key:
                url = "https://api.search.brave.com/res/v1/web/search"
                headers = {
                    'X-Subscription-Token': api_key,
                    'Accept': 'application/json'
                }
                params = {
                    'q': query,
                    'count': 10,
                    'offset': (pageno - 1) * 10,
                    'country': 'CN' if language.startswith('zh') else 'US'
                }
                
                async with self.session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        for item in data.get('web', {}).get('results', []):
                            results.append({
                                'title': item.get('title', ''),
                                'content': item.get('description', ''),
                                'url': item.get('url', ''),
                                'engine': 'brave',
                                'score': 7.5,
                                'category': 'general'
                            })
                        
                        return results
                        
        except Exception as e:
            logger.warning(f"Brave搜索失败: {e}")
        
        return []
    
    def _parse_bing_results(self, html: str) -> List[Dict]:
        """解析Bing搜索结果页面"""
        results = []
        try:
            # 简单的正则表达式解析（生产环境建议使用BeautifulSoup）
            # 这里只是示例实现
            title_pattern = r'<h2><a[^>]*href="([^"]*)"[^>]*>([^<]*)</a></h2>'
            desc_pattern = r'<p[^>]*>([^<]*)</p>'
            
            titles = re.findall(title_pattern, html)
            for i, (url, title) in enumerate(titles[:5]):  # 限制结果数量
                results.append({
                    'title': title.strip(),
                    'content': f"来自Bing的搜索结果: {title}",
                    'url': url,
                    'engine': 'bing',
                    'score': 6.5,
                    'category': 'general'
                })
                
        except Exception as e:
            logger.warning(f"解析Bing结果失败: {e}")
        
        return results
    
    def _merge_results(self, results: List, query: str) -> Dict[str, Any]:
        """合并多个引擎的搜索结果"""
        merged = []
        
        for result in results:
            if isinstance(result, list):
                merged.extend(result)
            elif isinstance(result, Exception):
                logger.warning(f"搜索引擎返回异常: {result}")
        
        # 去重和排序
        seen_urls = set()
        unique_results = []
        
        for item in merged:
            url = item.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(item)
        
        # 按评分排序
        unique_results.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return {
            "search": {
                "q": query,
                "pageno": 1,
                "lang": "zh-CN",
                "safesearch": 1,
                "timerange": None,
            },
            "results": unique_results[:20],  # 限制结果数量
            "infoboxes": [],
            "suggestions": [f"{query} 详细信息", f"{query} 最新动态"],
            "answers": [],
            "paging": {"current": 1, "next": 2},
            "number_of_results": len(unique_results),
        }
    
    def _empty_result(self, query: str) -> Dict[str, Any]:
        """返回空搜索结果"""
        return {
            "search": {
                "q": query,
                "pageno": 1,
                "lang": "zh-CN",
                "safesearch": 1,
                "timerange": None,
            },
            "results": [],
            "infoboxes": [],
            "suggestions": [],
            "answers": [],
            "paging": {"current": 1, "next": 1},
            "number_of_results": 0,
        }
    
    async def suggest(self, query: str) -> List[str]:
        """获取搜索建议"""
        try:
            if not self.initialized:
                await self.initialize()
            
            # 使用DuckDuckGo的建议API
            url = "https://duckduckgo.com/ac/"
            params = {'q': query}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return [item['phrase'] for item in data if 'phrase' in item]
                    
        except Exception as e:
            logger.error(f"获取搜索建议失败: {e}")
        
        return []
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.initialized:
                await self.initialize()
            
            # 执行简单搜索测试
            test_result = await self.search("test", engines=["duckduckgo"])
            
            return {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'initialized': self.initialized,
                'engines': list(self.engines.keys()),
                'test_results': len(test_result.get('results', [])),
                'stats': self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'initialized': self.initialized,
                'engines': list(self.engines.keys())
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = max(self.stats['total_requests'], 1)
        avg_response_time = self.stats['total_response_time'] / max(self.stats['successful_requests'], 1)
        success_rate = self.stats['successful_requests'] / total_requests
        
        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'cache_hits': self.stats['cache_hits']
        }
    
    async def close(self):
        """清理资源"""
        if self.session:
            await self.session.close()
        logger.info("备用搜索适配器已关闭")
