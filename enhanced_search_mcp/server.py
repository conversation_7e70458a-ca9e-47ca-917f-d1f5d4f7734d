#!/usr/bin/env python3
"""
Enhanced Search Engine MCP服务器
使用Server-Sent Events传输，兼容Agent-Zero等MCP客户端
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional
from fastmcp import FastMCP, Context
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

# 导入模块
from enhanced_search_engine import EnhancedSearchEngine
from searxng_windows_adapter import SearXNGWindowsAdapter
from fallback_search_adapter import FallbackSearchAdapter
from config import load_config, get_config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastMCP服务器实例
mcp = FastMCP(
    name="Enhanced Search Engine MCP Server",
    instructions="""
    专业增强搜索引擎MCP服务器

    🌐 技术特色：
    - 使用Server-Sent Events传输协议
    - 兼容Agent-Zero等MCP客户端
    - 实时双向通信支持
    - 更好的网络兼容性

    🔍 核心功能：
    - 多轮搜索策略（基础+扩展+相关主题）
    - 智能结果质量评估和排序
    - 自动摘要生成
    - 搜索结果缓存优化

    🎯 搜索深度：
    - basic: 仅基础搜索
    - deep: 基础+扩展搜索（默认）
    - comprehensive: 基础+扩展+相关搜索

    🚀 适用场景：
    - 深度研究和信息收集
    - 全面的主题分析
    - 高质量信息筛选
    - 智能搜索建议

    数据源：SearXNG多引擎聚合搜索
    传输协议：Server-Sent Events (SSE)
    """
)

# 全局组件实例
config: Optional[Any] = None
searxng_adapter: Optional[SearXNGWindowsAdapter] = None
fallback_adapter: Optional[FallbackSearchAdapter] = None
search_engine: Optional[EnhancedSearchEngine] = None

async def initialize_components():
    """初始化组件"""
    global config, searxng_adapter, fallback_adapter, search_engine

    try:
        # 加载配置
        config = load_config()
        logger.info("配置加载成功")

        # 尝试创建SearXNG Windows适配器
        try:
            searxng_adapter = SearXNGWindowsAdapter(
                base_url=config.SEARXNG_URL,
                timeout=config.SEARXNG_TIMEOUT,
                max_retries=config.SEARXNG_MAX_RETRIES
            )
            logger.info(f"SearXNG适配器创建成功: {config.SEARXNG_URL}")

            # 初始化适配器
            await searxng_adapter.initialize()
            logger.info("SearXNG适配器初始化完成")

            # 测试SearXNG连接
            health_status = await searxng_adapter.health_check()
            if health_status['status'] == 'healthy':
                logger.info("SearXNG连接测试成功，使用SearXNG适配器")
                # 创建搜索引擎
                search_engine = EnhancedSearchEngine(searxng_adapter, config)
                logger.info("增强搜索引擎创建成功（使用SearXNG）")
                return
            else:
                logger.warning(f"SearXNG连接异常: {health_status}")
                raise Exception("SearXNG不可用")

        except Exception as e:
            logger.warning(f"SearXNG适配器初始化失败: {e}")
            logger.info("切换到备用搜索适配器...")

            # 创建备用搜索适配器
            fallback_adapter = FallbackSearchAdapter(
                timeout=config.SEARXNG_TIMEOUT,
                max_retries=config.SEARXNG_MAX_RETRIES
            )
            logger.info("备用搜索适配器创建成功")

            # 初始化备用适配器
            await fallback_adapter.initialize()
            logger.info("备用搜索适配器初始化完成")

            # 测试备用适配器连接
            health_status = await fallback_adapter.health_check()
            if health_status['status'] == 'healthy':
                logger.info("备用搜索适配器连接测试成功")
                # 创建搜索引擎
                search_engine = EnhancedSearchEngine(fallback_adapter, config)
                logger.info("增强搜索引擎创建成功（使用备用适配器）")
            else:
                logger.error(f"备用搜索适配器也不可用: {health_status}")
                raise Exception("所有搜索适配器都不可用")

    except Exception as e:
        logger.error(f"组件初始化失败: {e}")
        raise

@mcp.tool()
async def enhanced_search(
    query: str,
    search_depth: str = "deep",
    max_results: int = 20,
    enable_cache: bool = True,
    ctx: Context = None
) -> str:
    """
    执行增强搜索

    Args:
        query: 搜索查询内容
        search_depth: 搜索深度 ("basic", "deep", "comprehensive")
        max_results: 最大结果数量 (1-50)
        enable_cache: 是否启用缓存
        ctx: MCP上下文对象

    Returns:
        格式化的搜索结果和摘要
    """
    try:
        # 参数验证
        if not query or not query.strip():
            return "❌ 请提供有效的搜索查询内容"

        if search_depth not in ["basic", "deep", "comprehensive"]:
            return "❌ 搜索深度必须是: basic, deep, comprehensive"

        if not (1 <= max_results <= 50):
            return "❌ 最大结果数量必须在1-50之间"

        # 使用上下文记录日志
        if ctx:
            await ctx.info(f"🔍 执行增强搜索: '{query}' (深度: {search_depth}, 最大结果: {max_results})")

        logger.info(f"执行增强搜索: '{query}' (深度: {search_depth}, 最大结果: {max_results})")

        # 确保组件已初始化
        if search_engine is None:
            await initialize_components()

        # 执行搜索
        result = await search_engine.execute(
            query=query,
            depth=search_depth,
            max_results=max_results,
            enable_cache=enable_cache
        )

        return result

    except Exception as e:
        error_msg = f"增强搜索失败: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)
        return error_msg

@mcp.tool()
async def search_suggestions(query: str, ctx: Context = None) -> str:
    """
    获取搜索建议

    Args:
        query: 原始查询
        ctx: MCP上下文对象

    Returns:
        搜索建议列表
    """
    try:
        if not query or not query.strip():
            return "❌ 请提供有效的查询内容"

        if ctx:
            await ctx.info(f"💡 生成搜索建议: '{query}'")

        logger.info(f"生成搜索建议: '{query}'")

        # 确保组件已初始化
        if search_engine is None:
            await initialize_components()

        suggestions = await search_engine.generate_search_suggestions(query)

        if suggestions:
            return f"针对'{query}'的搜索建议：\n" + "\n".join([f"• {s}" for s in suggestions])
        else:
            return f"暂无针对'{query}'的搜索建议"

    except Exception as e:
        error_msg = f"生成搜索建议失败: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)
        return error_msg

@mcp.tool()
async def search_stats(ctx: Context = None) -> str:
    """
    获取搜索统计信息

    Args:
        ctx: MCP上下文对象

    Returns:
        统计信息
    """
    try:
        if search_engine is None:
            return "搜索引擎未初始化"

        stats = search_engine.get_stats()

        # 获取适配器统计信息
        if searxng_adapter:
            adapter_stats = searxng_adapter.get_stats()
            adapter_type = "SearXNG适配器"
        elif fallback_adapter:
            adapter_stats = fallback_adapter.get_stats()
            adapter_type = "备用搜索适配器"
        else:
            adapter_stats = {}
            adapter_type = "未知适配器"

        return f"""📊 增强搜索引擎统计信息

🔍 搜索引擎统计:
• 总搜索次数: {stats['total_searches']}
• 成功搜索: {stats['successful_searches']}
• 失败搜索: {stats['failed_searches']}
• 成功率: {(stats['successful_searches']/max(stats['total_searches'], 1)*100):.1f}%
• 处理结果总数: {stats['total_results_processed']}
• 平均质量评分: {stats['avg_quality_score']:.2f}/10

🌐 SearXNG适配器统计:
• 总请求次数: {searxng_stats.get('total_requests', 0)}
• 成功请求: {searxng_stats.get('successful_requests', 0)}
• 失败请求: {searxng_stats.get('failed_requests', 0)}
• 平均响应时间: {searxng_stats.get('avg_response_time', 0):.2f}秒
• 成功率: {(searxng_stats.get('success_rate', 0)*100):.1f}%

💡 技术特色:
• ✅ 使用Server-Sent Events传输协议
• ✅ 兼容Agent-Zero等更多客户端
• ✅ 实时双向通信支持
• ✅ 更好的网络兼容性

⏰ 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    except Exception as e:
        error_msg = f"获取统计信息失败: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)
        return error_msg

@mcp.tool()
async def system_info(ctx: Context = None) -> str:
    """
    获取系统信息

    Args:
        ctx: MCP上下文对象

    Returns:
        系统信息
    """
    try:
        if ctx:
            await ctx.info("📋 获取系统信息...")

        # 获取健康状态
        health_status = await searxng_adapter.health_check() if searxng_adapter else {'status': 'not_initialized'}

        # 获取Python环境信息
        python_version = sys.version.split()[0]
        python_path = sys.executable

        return f"""🌐 Enhanced Search MCP服务器系统信息

🔧 版本信息:
• 服务器版本: v2.0
• 运行模式: Windows + SSE传输
• MCP协议: Server-Sent Events
• FastMCP版本: 2.11.0+

🐍 Python环境:
• Python版本: {python_version}
• Python路径: {python_path}
• 虚拟环境: uv (.venv)

🌐 SearXNG适配器状态:
• 类型: Windows HTTP适配器
• 状态: {health_status.get('status', 'unknown')}
• 初始化: {'✅' if health_status.get('initialized', False) else '❌'}
• 基础URL: {health_status.get('base_url', 'unknown')}

💡 技术优势:
• ✅ 使用Server-Sent Events传输协议
• ✅ 兼容Agent-Zero等更多客户端
• ✅ 实时双向通信支持
• ✅ 更好的网络兼容性
• ✅ 支持流式响应

🌐 支持的搜索引擎:
• Google, Bing, DuckDuckGo, Brave
• 以及SearXNG支持的所有其他引擎
• 智能引擎选择和负载均衡

⏰ 系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    except Exception as e:
        error_msg = f"获取系统信息失败: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)
        return error_msg

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("SEARXNG_URL"):
        logger.warning("未设置SEARXNG_URL环境变量，使用默认值 http://localhost:8888")

    # 获取配置
    host = os.getenv("MCP_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_PORT", "8883"))

    logger.info("🚀 启动增强搜索引擎MCP服务器...")
    logger.info(f"📡 SSE端点: http://{host}:{port}/sse")
    logger.info(f"📨 消息端点: http://{host}:{port}/messages/")
    logger.info("🌐 传输协议: Server-Sent Events")
    logger.info("✨ 功能: 多轮搜索、智能排序、自动摘要")

    # 使用FastMCP的SSE传输方式
    mcp.run(transport="sse", host=host, port=port)
