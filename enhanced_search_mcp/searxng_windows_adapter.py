#!/usr/bin/env python3
"""
SearXNG Windows适配器 - 使用HTTP API方式，兼容Windows环境
在Windows环境中通过HTTP API访问SearXNG实例
"""

import asyncio
import logging
import os
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp
import json

logger = logging.getLogger(__name__)

class SearXNGWindowsAdapter:
    """SearXNG Windows适配器 - 通过HTTP API访问"""
    
    def __init__(self, base_url: str = None, timeout: int = 30, max_retries: int = 3):
        self.base_url = base_url or os.getenv("SEARXNG_URL", "http://localhost:8888")
        self.timeout = timeout
        self.max_retries = max_retries
        self.initialized = False
        
        # 默认搜索引擎配置
        self.default_engines = ["google", "bing", "brave", "duckduckgo"]
        self.categories = "general"
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'cache_hits': 0
        }
        
        # HTTP会话
        self.session = None
    
    async def initialize(self):
        """初始化HTTP会话"""
        if self.initialized:
            return
        
        try:
            logger.info("初始化SearXNG Windows适配器...")
            
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Enhanced-Search-MCP/1.0',
                    'Accept': 'application/json',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            )
            
            self.initialized = True
            logger.info("SearXNG Windows适配器初始化完成")
            
            # 测试连接
            await self._test_connection()
            
        except Exception as e:
            logger.error(f"SearXNG Windows适配器初始化失败: {e}")
            raise
    
    async def _test_connection(self):
        """测试SearXNG连接"""
        try:
            async with self.session.get(f"{self.base_url}/") as response:
                if response.status == 200:
                    logger.info("SearXNG连接测试成功")
                else:
                    logger.warning(f"SearXNG连接异常，状态码: {response.status}")
        except Exception as e:
            logger.warning(f"SearXNG连接测试失败: {e}")
            logger.info("将尝试使用备用搜索方式")
    
    async def search(
        self, 
        query: str, 
        engines: Optional[List[str]] = None,
        categories: Optional[str] = None,
        language: str = "zh-CN",
        time_range: Optional[str] = None,
        safesearch: int = 1,
        pageno: int = 1
    ) -> Dict[str, Any]:
        """
        执行搜索查询
        
        Args:
            query: 搜索查询
            engines: 搜索引擎列表
            categories: 搜索类别
            language: 搜索语言
            time_range: 时间范围
            safesearch: 安全搜索级别
            pageno: 页码
        
        Returns:
            搜索结果字典
        """
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        try:
            # 确保已初始化
            if not self.initialized:
                await self.initialize()
            
            # 构建搜索参数
            params = {
                'q': query,
                'format': 'json',
                'lang': language,
                'safesearch': safesearch,
                'pageno': pageno,
            }
            
            # 添加引擎参数
            if engines:
                params['engines'] = ','.join(engines)
            else:
                params['engines'] = ','.join(self.default_engines)
            
            # 添加类别参数
            if categories:
                params['categories'] = categories
            else:
                params['categories'] = self.categories
            
            # 添加时间范围
            if time_range:
                params['time_range'] = time_range
            
            # 执行搜索请求
            search_url = f"{self.base_url}/search"
            
            async with self.session.get(search_url, params=params) as response:
                if response.status == 200:
                    result_data = await response.json()
                    
                    # 更新统计信息
                    response_time = time.time() - start_time
                    self.stats['successful_requests'] += 1
                    self.stats['total_response_time'] += response_time
                    
                    logger.info(f"搜索完成: '{query}' ({response_time:.2f}秒, {len(result_data.get('results', []))}个结果)")
                    
                    return result_data
                else:
                    # 如果SearXNG不可用，使用备用搜索
                    logger.warning(f"SearXNG API返回错误状态: {response.status}")
                    return await self._fallback_search(query)
                    
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"搜索失败: {e}")
            
            # 尝试备用搜索
            try:
                return await self._fallback_search(query)
            except Exception as fallback_error:
                logger.error(f"备用搜索也失败: {fallback_error}")
                raise e
    
    async def _fallback_search(self, query: str) -> Dict[str, Any]:
        """备用搜索方法 - 当SearXNG不可用时使用"""
        logger.info(f"使用备用搜索: '{query}'")
        
        # 这里可以实现其他搜索API的调用
        # 例如：DuckDuckGo API, Bing API等
        
        # 模拟搜索结果
        fallback_results = {
            "search": {
                "q": query,
                "pageno": 1,
                "lang": "zh-CN",
                "safesearch": 1,
                "timerange": None,
            },
            "results": [
                {
                    "title": f"关于'{query}'的搜索结果",
                    "content": f"这是一个关于'{query}'的模拟搜索结果。SearXNG服务当前不可用，正在使用备用搜索方式。",
                    "url": f"https://example.com/search?q={query}",
                    "engine": "fallback",
                    "score": 5.0,
                    "category": "general",
                    "publishedDate": datetime.now().isoformat(),
                }
            ],
            "infoboxes": [],
            "suggestions": [f"{query} 详细信息", f"{query} 最新动态"],
            "answers": [],
            "paging": {"current": 1, "next": 2},
            "number_of_results": 1,
        }
        
        return fallback_results
    
    async def suggest(self, query: str) -> List[str]:
        """获取搜索建议"""
        try:
            if not self.initialized:
                await self.initialize()
            
            params = {
                'q': query,
                'format': 'json'
            }
            
            suggest_url = f"{self.base_url}/autocompleter"
            
            async with self.session.get(suggest_url, params=params) as response:
                if response.status == 200:
                    suggestions = await response.json()
                    return suggestions if isinstance(suggestions, list) else []
                else:
                    return []
                    
        except Exception as e:
            logger.error(f"获取搜索建议失败: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.initialized:
                await self.initialize()
            
            # 执行简单搜索测试
            test_result = await self.search("test", engines=["duckduckgo"])
            
            return {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'initialized': self.initialized,
                'base_url': self.base_url,
                'test_results': len(test_result.get('results', [])),
                'stats': self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'initialized': self.initialized,
                'base_url': self.base_url
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = max(self.stats['total_requests'], 1)
        avg_response_time = self.stats['total_response_time'] / max(self.stats['successful_requests'], 1)
        success_rate = self.stats['successful_requests'] / total_requests
        
        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'cache_hits': self.stats['cache_hits']
        }
    
    async def close(self):
        """清理资源"""
        if self.session:
            await self.session.close()
        logger.info("SearXNG Windows适配器已关闭")
