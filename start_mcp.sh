#!/bin/bash

# Enhanced Search MCP服务器 - 直接版本启动脚本
# 直接调用SearXNG Python模块，无需HTTP API，解决端口冲突问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}🚀 启动Enhanced Search MCP服务器 (直接版本)${NC}"
echo -e "${PURPLE}=================================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}MCP目录: $MCP_DIR${NC}"

# 初始化conda
echo -e "${YELLOW}🐍 初始化conda环境...${NC}"
source ~/miniconda3/etc/profile.d/conda.sh

# 激活searxng环境
echo -e "${YELLOW}🔄 激活searxng环境...${NC}"
conda activate /home/<USER>/miniconda/envs/searxng
echo -e "${GREEN}✅ 已激活searxng环境${NC}"

# 检查并升级FastMCP到最新版本
echo -e "${YELLOW}📦 检查FastMCP版本...${NC}"
pip install --upgrade fastmcp>=2.11.0
echo -e "${GREEN}✅ FastMCP已更新到最新版本${NC}"

# 检查关键依赖
echo -e "${YELLOW}📦 检查关键依赖...${NC}"
python -c "import fastmcp; print(f'FastMCP版本: {fastmcp.__version__}')" 2>/dev/null && echo -e "${GREEN}✅ fastmcp${NC}" || echo -e "${RED}❌ fastmcp${NC}"
python -c "import searx; print('SearXNG模块可用')" 2>/dev/null && echo -e "${GREEN}✅ searx (SearXNG)${NC}" || echo -e "${RED}❌ searx (SearXNG)${NC}"
python -c "import aiohttp" 2>/dev/null && echo -e "${GREEN}✅ aiohttp${NC}" || echo -e "${RED}❌ aiohttp${NC}"

# 检查端口占用并清理
cleanup_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  清理端口 $port ($service)...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

cleanup_port 8882 "MCP服务器(直接版本)"
cleanup_port 8885 "SearXNG HTTP服务" # 清理可能的冲突端口

# 创建日志目录
mkdir -p "$MCP_DIR/logs"

# 设置环境变量
export MCP_HOST="0.0.0.0"
export MCP_PORT="8882"
export LOG_LEVEL="INFO"

# 显示优势特性
echo -e "${PURPLE}🔥 直接版本特色优势:${NC}"
echo -e "${GREEN}✅ 直接调用SearXNG Python模块，无需HTTP API${NC}"
echo -e "${GREEN}✅ 解决端口冲突问题，完全独立运行${NC}"
echo -e "${GREEN}✅ 零网络延迟，更快响应速度${NC}"
echo -e "${GREEN}✅ 更高稳定性，更好的错误处理${NC}"
echo ""

# 启动MCP服务器直接版本
echo -e "${CYAN}🤖 启动Enhanced Search MCP服务器 (直接版本)...${NC}"
cd "$MCP_DIR"

# 检查必要文件
if [ -f "server_direct.py" ] && [ -f "searxng_direct_adapter.py" ]; then
    echo -e "${GREEN}✅ MCP服务器直接版本文件存在${NC}"

    echo -e "${GREEN}🚀 启动MCP服务器直接版本...${NC}"
    echo -e "${BLUE}📡 MCP端点: http://localhost:8882/mcp${NC}"
    echo -e "${BLUE}🔄 使用Streamable HTTP传输协议${NC}"
    echo -e "${BLUE}🔥 直接调用SearXNG模块，无HTTP依赖${NC}"
    echo -e "${BLUE}✨ 支持FastMCP 2.0所有特性${NC}"
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 停止服务器${NC}"
    echo ""

    # 启动服务器 (前台运行)
    python server_direct.py
else
    echo -e "${RED}❌ MCP服务器直接版本文件缺失${NC}"
    echo -e "${RED}请确保以下文件存在:${NC}"
    echo -e "${RED}  - server_direct.py${NC}"
    echo -e "${RED}  - searxng_direct_adapter.py${NC}"
    exit 1
fi
