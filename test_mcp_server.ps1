# 测试Enhanced Search MCP服务器

param(
    [string]$ServerHost = "localhost",
    [int]$Port = 8883
)

Write-Host "🧪 测试Enhanced Search MCP服务器" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

$baseUrl = "http://${ServerHost}:${Port}"
$sseUrl = "$baseUrl/sse"
$messageUrl = "$baseUrl/messages/"

Write-Host "`n🌐 服务器信息:" -ForegroundColor Blue
Write-Host "   主机: $ServerHost" -ForegroundColor Gray
Write-Host "   端口: $Port" -ForegroundColor Gray
Write-Host "   SSE端点: $sseUrl" -ForegroundColor Gray
Write-Host "   消息端点: $messageUrl" -ForegroundColor Gray

# 测试1: 检查服务器是否在线
Write-Host "`n1️⃣ 检查服务器状态..." -ForegroundColor Yellow
try {
    $tcpTest = Test-NetConnection -ComputerName $ServerHost -Port $Port -WarningAction SilentlyContinue
    if ($tcpTest.TcpTestSucceeded) {
        Write-Host "✅ 服务器端口 $Port 可访问" -ForegroundColor Green
    } else {
        Write-Host "❌ 服务器端口 $Port 不可访问" -ForegroundColor Red
        Write-Host "💡 请确保MCP服务器正在运行: .\start_mcp_server.ps1" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ 网络连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试2: 检查SSE端点
Write-Host "`n2️⃣ 测试SSE端点..." -ForegroundColor Yellow

try {
    # 测试SSE端点是否存在
    $response = Invoke-WebRequest -Uri $sseUrl -Method GET -TimeoutSec 5
    Write-Host "✅ SSE端点响应: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "⚠️ SSE端点状态码: $statusCode" -ForegroundColor Yellow
    
    if ($statusCode -eq 200 -or $statusCode -eq 101) {
        Write-Host "   ✅ SSE端点正常工作" -ForegroundColor Green
    } elseif ($statusCode -eq 404) {
        Write-Host "   ❌ SSE端点不存在" -ForegroundColor Red
    } else {
        Write-Host "   ⚠️ SSE端点可能需要特殊处理" -ForegroundColor Yellow
    }
}

# 测试3: MCP初始化
Write-Host "`n3️⃣ 测试MCP初始化..." -ForegroundColor Yellow

$initPayload = @{
    jsonrpc = "2.0"
    id = 1
    method = "initialize"
    params = @{
        protocolVersion = "2024-11-05"
        capabilities = @{}
        clientInfo = @{
            name = "test-client"
            version = "1.0.0"
        }
    }
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri $messageUrl -Method POST -Body $initPayload -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ MCP初始化成功!" -ForegroundColor Green
    Write-Host "   服务器名称: $($response.result.serverInfo.name)" -ForegroundColor Gray
    Write-Host "   协议版本: $($response.result.protocolVersion)" -ForegroundColor Gray
    
} catch {
    Write-Host "❌ MCP初始化失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "   状态码: $statusCode" -ForegroundColor Red
    }
    exit 1
}

# 测试4: 工具列表
Write-Host "`n4️⃣ 测试工具列表..." -ForegroundColor Yellow

$toolsPayload = @{
    jsonrpc = "2.0"
    id = 2
    method = "tools/list"
    params = @{}
} | ConvertTo-Json -Depth 10

try {
    $toolsResponse = Invoke-RestMethod -Uri $messageUrl -Method POST -Body $toolsPayload -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ 工具列表获取成功!" -ForegroundColor Green
    Write-Host "   可用工具数量: $($toolsResponse.result.tools.Count)" -ForegroundColor Gray
    
    foreach ($tool in $toolsResponse.result.tools) {
        Write-Host "   • $($tool.name): $($tool.description.Substring(0, [Math]::Min(50, $tool.description.Length)))..." -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ 工具列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 搜索功能
Write-Host "`n5️⃣ 测试搜索功能..." -ForegroundColor Yellow

$searchPayload = @{
    jsonrpc = "2.0"
    id = 3
    method = "tools/call"
    params = @{
        name = "enhanced_search"
        arguments = @{
            query = "Python编程"
            search_depth = "basic"
            max_results = 3
        }
    }
} | ConvertTo-Json -Depth 10

try {
    Write-Host "   发送搜索请求..." -ForegroundColor Gray
    $searchResponse = Invoke-RestMethod -Uri $messageUrl -Method POST -Body $searchPayload -ContentType "application/json" -TimeoutSec 30
    Write-Host "✅ 搜索功能测试成功!" -ForegroundColor Green
    
    if ($searchResponse.result -and $searchResponse.result.content) {
        $content = $searchResponse.result.content
        if ($content.Length -gt 300) {
            Write-Host "   搜索结果预览:" -ForegroundColor Cyan
            Write-Host "   $($content.Substring(0, 300))..." -ForegroundColor Gray
        } else {
            Write-Host "   搜索结果:" -ForegroundColor Cyan
            Write-Host "   $content" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ 搜索功能测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试6: 系统信息
Write-Host "`n6️⃣ 测试系统信息..." -ForegroundColor Yellow

$sysInfoPayload = @{
    jsonrpc = "2.0"
    id = 4
    method = "tools/call"
    params = @{
        name = "system_info"
        arguments = @{}
    }
} | ConvertTo-Json -Depth 10

try {
    $sysResponse = Invoke-RestMethod -Uri $messageUrl -Method POST -Body $sysInfoPayload -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ 系统信息获取成功!" -ForegroundColor Green
    
    if ($sysResponse.result -and $sysResponse.result.content) {
        $lines = $sysResponse.result.content -split "`n" | Select-Object -First 8
        Write-Host "   系统信息预览:" -ForegroundColor Cyan
        foreach ($line in $lines) {
            Write-Host "   $line" -ForegroundColor Gray
        }
        Write-Host "   ..." -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 系统信息获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 测试完成!" -ForegroundColor Green
Write-Host "============" -ForegroundColor Green

Write-Host "`n📋 服务器配置信息:" -ForegroundColor Cyan
Write-Host "SSE端点: $sseUrl" -ForegroundColor White
Write-Host "消息端点: $messageUrl" -ForegroundColor White
Write-Host "传输协议: Server-Sent Events" -ForegroundColor White
Write-Host "MCP协议版本: 2024-11-05" -ForegroundColor White

Write-Host "`n🔗 Agent-Zero配置:" -ForegroundColor Cyan
Write-Host @"
{
  "name": "enhanced-search",
  "type": "sse",
  "url": "$sseUrl",
  "description": "增强搜索引擎MCP服务器",
  "message_endpoint": "$messageUrl"
}
"@ -ForegroundColor White
