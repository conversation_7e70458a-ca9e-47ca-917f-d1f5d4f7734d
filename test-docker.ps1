# Docker环境测试脚本
Write-Host "🧪 Docker环境测试" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# 测试1: 检查Docker是否安装
Write-Host "`n1️⃣ 检查Docker安装..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker已安装: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker未安装或未在PATH中" -ForegroundColor Red
    exit 1
}

# 测试2: 检查Docker是否运行
Write-Host "`n2️⃣ 检查Docker服务..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info --format "{{.ServerVersion}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker服务正常运行 (版本: $dockerInfo)" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker服务未运行" -ForegroundColor Red
        Write-Host "请启动Docker Desktop" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ 无法连接到Docker服务" -ForegroundColor Red
    exit 1
}

# 测试3: 检查docker-compose
Write-Host "`n3️⃣ 检查Docker Compose..." -ForegroundColor Yellow
try {
    $composeVersion = docker-compose --version
    Write-Host "✅ Docker Compose可用: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose不可用" -ForegroundColor Red
    exit 1
}

# 测试4: 检查必要文件
Write-Host "`n4️⃣ 检查项目文件..." -ForegroundColor Yellow
$requiredFiles = @(
    "Dockerfile.searxng-mcp",
    "docker-compose.windows.yml",
    "searx/settings.yml",
    "enhanced_search_mcp/server.py",
    "enhanced_search_mcp/requirements.txt",
    "docker/start-services.sh"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (缺失)" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if (-not $allFilesExist) {
    Write-Host "`n❌ 部分必要文件缺失，请检查项目完整性" -ForegroundColor Red
    exit 1
}

# 测试5: 检查端口占用
Write-Host "`n5️⃣ 检查端口占用..." -ForegroundColor Yellow
$ports = @(8885, 8881, 6379)
$portsOk = $true

foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "⚠️ 端口 $port 已被占用" -ForegroundColor Yellow
        $portsOk = $false
    } else {
        Write-Host "✅ 端口 $port 可用" -ForegroundColor Green
    }
}

if (-not $portsOk) {
    Write-Host "`n⚠️ 部分端口被占用，可能影响服务启动" -ForegroundColor Yellow
    Write-Host "可以使用以下命令查看占用进程:" -ForegroundColor Gray
    Write-Host "netstat -ano | findstr :8885" -ForegroundColor Gray
}

# 测试6: 检查系统资源
Write-Host "`n6️⃣ 检查系统资源..." -ForegroundColor Yellow
$memory = Get-CimInstance -ClassName Win32_ComputerSystem
$totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
$freeSpace = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DeviceID='C:'" | Select-Object -ExpandProperty FreeSpace
$freeSpaceGB = [math]::Round($freeSpace / 1GB, 2)

Write-Host "💾 总内存: $totalMemoryGB GB" -ForegroundColor Blue
Write-Host "💿 C盘可用空间: $freeSpaceGB GB" -ForegroundColor Blue

if ($totalMemoryGB -lt 4) {
    Write-Host "⚠️ 内存可能不足，推荐至少4GB" -ForegroundColor Yellow
}

if ($freeSpaceGB -lt 2) {
    Write-Host "⚠️ 磁盘空间可能不足，推荐至少2GB可用空间" -ForegroundColor Yellow
}

# 测试7: 测试Docker镜像拉取
Write-Host "`n7️⃣ 测试Docker镜像拉取..." -ForegroundColor Yellow
try {
    Write-Host "正在测试拉取基础镜像..." -ForegroundColor Gray
    docker pull python:3.11-slim --quiet
    Write-Host "✅ 基础镜像拉取成功" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 镜像拉取失败，可能是网络问题" -ForegroundColor Yellow
}

# 总结
Write-Host "`n📊 测试总结" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan

if ($allFilesExist -and $portsOk) {
    Write-Host "🎉 环境检查通过！可以开始部署" -ForegroundColor Green
    Write-Host "`n🚀 下一步操作:" -ForegroundColor Yellow
    Write-Host "1. 运行: .\start-docker.ps1" -ForegroundColor White
    Write-Host "2. 等待服务启动完成" -ForegroundColor White
    Write-Host "3. 访问: http://localhost:8885" -ForegroundColor White
} else {
    Write-Host "⚠️ 环境检查发现问题，请先解决后再部署" -ForegroundColor Yellow
}

Write-Host "`n💡 提示:" -ForegroundColor Blue
Write-Host "- 首次启动需要下载依赖，可能需要较长时间" -ForegroundColor Gray
Write-Host "- 确保网络连接正常以下载Docker镜像" -ForegroundColor Gray
Write-Host "- 如有问题请查看 DOCKER_DEPLOYMENT.md 文档" -ForegroundColor Gray
