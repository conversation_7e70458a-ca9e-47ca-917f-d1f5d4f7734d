# 验证Enhanced Search MCP服务器安装

Write-Host "🔍 验证Enhanced Search MCP服务器安装" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

try {
    # 检查虚拟环境
    Write-Host "`n1️⃣ 检查虚拟环境..." -ForegroundColor Yellow
    $pythonPath = python -c "import sys; print(sys.executable)" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Python不可用，请激活虚拟环境" -ForegroundColor Red
        Write-Host "💡 运行: .\.venv\Scripts\Activate.ps1" -ForegroundColor Yellow
        exit 1
    }
    
    if ($pythonPath.Contains(".venv")) {
        Write-Host "✅ 虚拟环境已激活" -ForegroundColor Green
        Write-Host "   路径: $pythonPath" -ForegroundColor Gray
    } else {
        Write-Host "⚠️ 可能未使用虚拟环境" -ForegroundColor Yellow
        Write-Host "   路径: $pythonPath" -ForegroundColor Gray
    }

    # 检查Python版本
    $pythonVersion = python --version
    Write-Host "   版本: $pythonVersion" -ForegroundColor Gray

    # 检查核心依赖
    Write-Host "`n2️⃣ 检查核心依赖..." -ForegroundColor Yellow
    $corePackages = @{
        "fastmcp" = "FastMCP框架"
        "fastapi" = "FastAPI框架" 
        "uvicorn" = "ASGI服务器"
        "aiohttp" = "异步HTTP客户端"
        "pydantic" = "数据验证"
    }

    $coreInstalled = $true
    foreach ($package in $corePackages.Keys) {
        try {
            $version = python -c "import $package; print($package.__version__)" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "   ✅ $($corePackages[$package]): $version" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $($corePackages[$package]): 未安装" -ForegroundColor Red
                $coreInstalled = $false
            }
        } catch {
            Write-Host "   ❌ $($corePackages[$package]): 检查失败" -ForegroundColor Red
            $coreInstalled = $false
        }
    }

    # 检查项目文件
    Write-Host "`n3️⃣ 检查项目文件..." -ForegroundColor Yellow
    $projectFiles = @{
        "enhanced_search_mcp\server_windows.py" = "Windows版本服务器"
        "enhanced_search_mcp\searxng_windows_adapter.py" = "Windows适配器"
        "enhanced_search_mcp\enhanced_search_engine.py" = "增强搜索引擎"
        "enhanced_search_mcp\config.py" = "配置管理"
        "enhanced_search_mcp\requirements.txt" = "依赖列表"
    }

    $filesExist = $true
    foreach ($file in $projectFiles.Keys) {
        if (Test-Path $file) {
            Write-Host "   ✅ $($projectFiles[$file]): 存在" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $($projectFiles[$file]): 缺失" -ForegroundColor Red
            $filesExist = $false
        }
    }

    # 测试导入项目模块
    Write-Host "`n4️⃣ 测试项目模块导入..." -ForegroundColor Yellow
    
    # 切换到项目目录进行测试
    $originalLocation = Get-Location
    Set-Location "enhanced_search_mcp"
    
    try {
        # 测试配置模块
        $configTest = python -c "from config import load_config; print('配置模块导入成功')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ 配置模块: 导入成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 配置模块: 导入失败" -ForegroundColor Red
        }

        # 测试Windows适配器
        $adapterTest = python -c "from searxng_windows_adapter import SearXNGWindowsAdapter; print('Windows适配器导入成功')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Windows适配器: 导入成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Windows适配器: 导入失败" -ForegroundColor Red
        }

        # 测试搜索引擎
        $engineTest = python -c "from enhanced_search_engine import EnhancedSearchEngine; print('搜索引擎导入成功')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ 搜索引擎: 导入成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 搜索引擎: 导入失败" -ForegroundColor Red
        }

        # 测试FastMCP
        $mcpTest = python -c "from fastmcp import FastMCP; print('FastMCP导入成功')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ FastMCP: 导入成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ FastMCP: 导入失败" -ForegroundColor Red
        }

    } finally {
        Set-Location $originalLocation
    }

    # 检查端口可用性
    Write-Host "`n5️⃣ 检查端口可用性..." -ForegroundColor Yellow
    $port = 8882
    $portInUse = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Host "   ⚠️ 端口 $port 已被占用" -ForegroundColor Yellow
        Write-Host "   进程ID: $($portInUse.OwningProcess)" -ForegroundColor Gray
    } else {
        Write-Host "   ✅ 端口 $port 可用" -ForegroundColor Green
    }

    # 显示环境统计
    Write-Host "`n6️⃣ 环境统计..." -ForegroundColor Yellow
    $packageCount = (uv pip list | Measure-Object -Line).Lines - 2
    Write-Host "   已安装包数量: $packageCount" -ForegroundColor Gray
    Write-Host "   虚拟环境大小: $((Get-ChildItem .venv -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB | ForEach-Object {[math]::Round($_, 2)}) MB" -ForegroundColor Gray

    # 总结
    Write-Host "`n📊 验证总结:" -ForegroundColor Cyan
    if ($coreInstalled -and $filesExist) {
        Write-Host "✅ 安装验证通过！可以启动服务器" -ForegroundColor Green
        
        Write-Host "`n🚀 启动选项:" -ForegroundColor Cyan
        Write-Host "1. 直接启动: python enhanced_search_mcp\server_windows.py" -ForegroundColor White
        Write-Host "2. 使用脚本: .\start_mcp_windows.ps1" -ForegroundColor White
        Write-Host "3. 一键启动: .\setup_and_start.ps1 -SkipInstall" -ForegroundColor White
        Write-Host "4. 开发模式: .\setup_and_start.ps1 -SkipInstall -Dev" -ForegroundColor White
        
        Write-Host "`n📡 服务器将在以下地址运行:" -ForegroundColor Cyan
        Write-Host "   - 本地: http://localhost:8882/mcp" -ForegroundColor White
        Write-Host "   - Docker: http://host.docker.internal:8882/mcp" -ForegroundColor White
        
    } else {
        Write-Host "❌ 安装验证失败，请检查上述错误" -ForegroundColor Red
        Write-Host "`n🔧 建议操作:" -ForegroundColor Yellow
        Write-Host "1. 重新安装依赖: .\install_dependencies.ps1" -ForegroundColor White
        Write-Host "2. 检查项目文件完整性" -ForegroundColor White
        Write-Host "3. 确保虚拟环境正确激活" -ForegroundColor White
    }

} catch {
    Write-Host "`n❌ 验证过程出错: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
