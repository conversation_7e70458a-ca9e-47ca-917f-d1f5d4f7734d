version: '3.8'

services:
  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: searxng-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - searxng-network

  # SearXNG + MCP服务器（Windows优化版）
  searxng-mcp:
    build:
      context: .
      dockerfile: Dockerfile.searxng-mcp
    container_name: searxng-mcp-server
    restart: unless-stopped
    ports:
      - "8885:8885"  # SearXNG Web界面
      - "8881:8881"  # MCP服务器
    volumes:
      # Windows路径映射
      - type: bind
        source: ./logs
        target: /app/logs
      - type: bind
        source: ./enhanced_search_mcp/logs
        target: /app/enhanced_search_mcp/logs
      - type: bind
        source: ./searx/settings.yml
        target: /app/searx/settings.yml
        read_only: true
    environment:
      # SearXNG配置
      - SEARXNG_PORT=8885
      - SEARXNG_BIND_ADDRESS=0.0.0.0
      - SEARXNG_SETTINGS_PATH=/app/searx/settings.yml
      - SEARXNG_SECRET=your-secret-key-change-this
      - SEARXNG_BASE_URL=http://localhost:8885
      
      # MCP服务器配置
      - MCP_HOST=0.0.0.0
      - MCP_PORT=8881
      - SEARXNG_URL=http://localhost:8885
      - LOG_LEVEL=INFO
      
      # Redis配置
      - REDIS_URL=redis://redis:6379
      - ENABLE_CACHE=true
      - CACHE_TTL=3600
      
      # 搜索配置
      - MAX_CONCURRENT_SEARCHES=5
      - DEFAULT_ENGINES=google,bing,brave
      - MAX_RESULTS_PER_QUERY=10
      - MAX_TOTAL_RESULTS=20
      
      # 质量评分权重
      - TITLE_RELEVANCE_WEIGHT=0.3
      - CONTENT_QUALITY_WEIGHT=0.25
      - SOURCE_AUTHORITY_WEIGHT=0.2
      - FRESHNESS_WEIGHT=0.15
      - ENGAGEMENT_WEIGHT=0.1
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8885/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - searxng-network

volumes:
  redis_data:
    driver: local

networks:
  searxng-network:
    driver: bridge
