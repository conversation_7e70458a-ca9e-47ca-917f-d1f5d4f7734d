# SearXNG + Enhanced Search MCP 启动指南

## 问题修复说明

原始的 `start_all_services.sh` 脚本存在以下问题：

1. **路径计算错误**: 脚本错误地将项目目录设置为父目录，导致无法找到 `enhanced_search_mcp` 目录
2. **Conda环境路径问题**: 环境激活路径不正确

## 修复内容

### 1. 修复了路径计算
```bash
# 修复前
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"  # 错误：指向父目录

# 修复后
PROJECT_DIR="$SCRIPT_DIR"               # 正确：指向当前目录
```

### 2. 修复了Conda环境激活
```bash
# 使用完整路径激活环境
conda activate /home/<USER>/miniconda/envs/searxng
```

## 启动方式

### 方式1: 完整启动 (推荐)
启动SearXNG和MCP服务器：
```bash
./start_all_services.sh
```

### 方式2: 仅启动MCP服务器
如果SearXNG已经在运行，可以只启动MCP服务器：
```bash
./start_mcp_only.sh
```

### 方式3: 测试服务器状态
测试MCP服务器是否正常运行：
```bash
./test_mcp_server.sh
```

## 服务端点

启动成功后，以下端点将可用：

- **SearXNG Web界面**: http://localhost:8885
- **SearXNG API**: http://localhost:8885/search
- **MCP SSE端点**: http://localhost:8881/mcp/sse
- **健康检查**: http://localhost:8881/health
- **工具列表**: http://localhost:8881/tools

## Agent-Zero MCP配置

在Agent-Zero中使用以下配置：

```json
{
  "name": "enhanced-search",
  "type": "sse",
  "url": "http://localhost:8881/mcp/sse",
  "description": "增强搜索引擎MCP服务器"
}
```

## 日志文件

- SearXNG日志: `logs/searxng.log`
- MCP服务器日志: `enhanced_search_mcp/logs/mcp_server.log`

## 故障排除

### 1. 端口占用
如果端口被占用，脚本会自动清理：
```bash
lsof -ti:8885 | xargs kill -9  # 清理SearXNG端口
lsof -ti:8881 | xargs kill -9  # 清理MCP端口
```

### 2. 依赖检查
脚本会自动检查关键依赖：
- fastmcp
- fastapi
- uvicorn
- aiohttp

### 3. 环境问题
确保conda环境正确激活：
```bash
conda info --envs  # 查看可用环境
conda activate /home/<USER>/miniconda/envs/searxng
```

## 功能特性

Enhanced Search MCP服务器提供：

- 🔍 多轮搜索策略（基础+扩展+相关主题）
- 🎯 智能结果质量评估和排序
- 📝 自动摘要生成
- 💾 搜索结果缓存优化
- 📊 搜索统计和监控

## 搜索深度选项

- `basic`: 仅基础搜索
- `deep`: 基础+扩展搜索（默认）
- `comprehensive`: 基础+扩展+相关搜索

## 停止服务

按 `Ctrl+C` 停止所有服务，脚本会自动清理进程和端口。
