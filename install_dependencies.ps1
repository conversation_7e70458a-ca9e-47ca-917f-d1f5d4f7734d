# 安装Enhanced Search MCP服务器依赖 - uv虚拟环境版本

Write-Host "📦 安装Enhanced Search MCP服务器依赖" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

try {
    # 检查虚拟环境是否激活
    $pythonPath = python -c "import sys; print(sys.executable)" 2>$null
    if ($LASTEXITCODE -ne 0 -or -not $pythonPath.Contains(".venv")) {
        Write-Host "❌ 虚拟环境未激活" -ForegroundColor Red
        Write-Host "💡 请先运行: .\.venv\Scripts\Activate.ps1" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "✅ 虚拟环境已激活" -ForegroundColor Green
    Write-Host "   Python路径: $pythonPath" -ForegroundColor Gray

    # 升级pip和基础工具
    Write-Host "`n⬆️ 升级基础工具..." -ForegroundColor Yellow
    uv pip install --upgrade pip setuptools wheel
    Write-Host "✅ 基础工具升级完成" -ForegroundColor Green

    # 安装核心MCP依赖
    Write-Host "`n📦 安装核心MCP依赖..." -ForegroundColor Yellow
    $corePackages = @(
        "fastmcp>=2.11.0",
        "fastapi>=0.104.0", 
        "uvicorn[standard]>=0.24.0"
    )
    
    foreach ($package in $corePackages) {
        Write-Host "   安装 $package..." -ForegroundColor Gray
        uv pip install $package
    }
    Write-Host "✅ 核心MCP依赖安装完成" -ForegroundColor Green

    # 安装HTTP客户端依赖
    Write-Host "`n🌐 安装HTTP客户端依赖..." -ForegroundColor Yellow
    $httpPackages = @(
        "aiohttp>=3.9.0",
        "httpx>=0.25.0",
        "requests>=2.31.0",
        "urllib3>=2.0.0",
        "certifi>=2023.0.0"
    )
    
    foreach ($package in $httpPackages) {
        Write-Host "   安装 $package..." -ForegroundColor Gray
        uv pip install $package
    }
    Write-Host "✅ HTTP客户端依赖安装完成" -ForegroundColor Green

    # 安装数据处理依赖
    Write-Host "`n📊 安装数据处理依赖..." -ForegroundColor Yellow
    $dataPackages = @(
        "pydantic>=2.5.0",
        "python-dotenv>=1.0.0",
        "python-dateutil>=2.8.0",
        "pyyaml>=6.0"
    )
    
    foreach ($package in $dataPackages) {
        Write-Host "   安装 $package..." -ForegroundColor Gray
        uv pip install $package
    }
    Write-Host "✅ 数据处理依赖安装完成" -ForegroundColor Green

    # 安装SearXNG相关依赖
    Write-Host "`n🔍 安装SearXNG相关依赖..." -ForegroundColor Yellow
    $searxngPackages = @(
        "babel>=2.12.0",
        "flask>=2.3.0",
        "jinja2>=3.1.0",
        "lxml>=4.9.0",
        "markupsafe>=2.1.0"
    )
    
    foreach ($package in $searxngPackages) {
        Write-Host "   安装 $package..." -ForegroundColor Gray
        uv pip install $package
    }
    Write-Host "✅ SearXNG相关依赖安装完成" -ForegroundColor Green

    # 安装日志和监控依赖
    Write-Host "`n📝 安装日志和监控依赖..." -ForegroundColor Yellow
    $loggingPackages = @(
        "structlog>=23.2.0"
    )
    
    foreach ($package in $loggingPackages) {
        Write-Host "   安装 $package..." -ForegroundColor Gray
        uv pip install $package
    }
    Write-Host "✅ 日志和监控依赖安装完成" -ForegroundColor Green

    # 安装可选的缓存依赖
    Write-Host "`n💾 安装缓存依赖（可选）..." -ForegroundColor Yellow
    try {
        uv pip install redis>=5.0.0 aioredis>=2.0.0
        Write-Host "✅ 缓存依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 缓存依赖安装失败，但不影响核心功能" -ForegroundColor Yellow
    }

    # 安装开发工具（可选）
    Write-Host "`n🛠️ 安装开发工具（可选）..." -ForegroundColor Yellow
    try {
        $devPackages = @(
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0"
        )
        
        foreach ($package in $devPackages) {
            Write-Host "   安装 $package..." -ForegroundColor Gray
            uv pip install $package
        }
        Write-Host "✅ 开发工具安装完成" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 开发工具安装失败，但不影响核心功能" -ForegroundColor Yellow
    }

    # 验证关键包安装
    Write-Host "`n🔍 验证关键包安装..." -ForegroundColor Yellow
    $criticalPackages = @{
        "fastmcp" = "FastMCP框架"
        "fastapi" = "FastAPI框架"
        "uvicorn" = "ASGI服务器"
        "aiohttp" = "异步HTTP客户端"
        "pydantic" = "数据验证"
        "python-dotenv" = "环境变量"
        "requests" = "HTTP请求"
        "pyyaml" = "YAML解析"
    }

    $allInstalled = $true
    foreach ($package in $criticalPackages.Keys) {
        try {
            $version = python -c "import $package; print($package.__version__)" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "   ✅ $($criticalPackages[$package]): $version" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $($criticalPackages[$package]): 未安装" -ForegroundColor Red
                $allInstalled = $false
            }
        } catch {
            Write-Host "   ❌ $($criticalPackages[$package]): 检查失败" -ForegroundColor Red
            $allInstalled = $false
        }
    }

    # 显示安装的包列表
    Write-Host "`n📋 已安装的包列表:" -ForegroundColor Cyan
    $packageCount = (uv pip list | Measure-Object -Line).Lines - 2  # 减去标题行
    Write-Host "   总计: $packageCount 个包" -ForegroundColor Gray
    
    # 显示环境信息
    Write-Host "`n📊 环境信息:" -ForegroundColor Cyan
    Write-Host "   Python版本: $(python --version)" -ForegroundColor Gray
    Write-Host "   Python路径: $pythonPath" -ForegroundColor Gray
    Write-Host "   虚拟环境: $(Get-Location)\.venv" -ForegroundColor Gray
    Write-Host "   uv版本: $(uv --version)" -ForegroundColor Gray

    if ($allInstalled) {
        Write-Host "`n🎉 所有依赖安装完成！" -ForegroundColor Green
        Write-Host "`n📝 下一步操作:" -ForegroundColor Cyan
        Write-Host "1. 启动Windows版本服务器: python enhanced_search_mcp\server_windows.py" -ForegroundColor White
        Write-Host "2. 或使用启动脚本: .\start_mcp_windows.ps1" -ForegroundColor White
        Write-Host "3. 或一键启动: .\setup_and_start.ps1 -SkipInstall" -ForegroundColor White
    } else {
        Write-Host "`n⚠️ 部分依赖安装失败，请检查错误信息" -ForegroundColor Yellow
        Write-Host "💡 可以尝试重新运行此脚本" -ForegroundColor Yellow
    }

} catch {
    Write-Host "`n❌ 依赖安装失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`n🔧 故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 确保虚拟环境已激活" -ForegroundColor White
    Write-Host "2. 检查网络连接" -ForegroundColor White
    Write-Host "3. 尝试清理pip缓存: uv pip cache clean" -ForegroundColor White
    Write-Host "4. 重新创建虚拟环境" -ForegroundColor White
    exit 1
}
