# uv虚拟环境设置脚本 - Windows版本

param(
    [string]$PythonVersion = "3.11",
    [switch]$Force = $false
)

Write-Host "🐍 设置uv虚拟环境 - Enhanced Search MCP Server" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

try {
    # 检查uv是否安装
    $uvVersion = uv --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ uv未安装，请先安装uv" -ForegroundColor Red
        Write-Host "💡 运行: Invoke-RestMethod https://astral.sh/uv/install.ps1 | Invoke-Expression" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "✅ uv版本: $uvVersion" -ForegroundColor Green

    # 检查当前目录
    $currentDir = Get-Location
    Write-Host "📁 当前目录: $currentDir" -ForegroundColor Blue

    # 检查是否已存在.venv
    if (Test-Path ".venv" -PathType Container) {
        if ($Force) {
            Write-Host "🗑️ 删除现有虚拟环境..." -ForegroundColor Yellow
            Remove-Item -Recurse -Force ".venv"
        } else {
            Write-Host "⚠️ 虚拟环境已存在" -ForegroundColor Yellow
            $recreate = Read-Host "是否重新创建? (y/N)"
            if ($recreate -eq 'y' -or $recreate -eq 'Y') {
                Remove-Item -Recurse -Force ".venv"
            } else {
                Write-Host "✅ 使用现有虚拟环境" -ForegroundColor Green
                # 激活现有环境并安装依赖
                & ".venv\Scripts\Activate.ps1"
                Write-Host "🔄 更新依赖包..." -ForegroundColor Yellow
                uv pip install -r enhanced_search_mcp/requirements.txt
                Write-Host "✅ 依赖更新完成！" -ForegroundColor Green
                exit 0
            }
        }
    }

    # 创建虚拟环境
    Write-Host "🏗️ 创建Python $PythonVersion 虚拟环境..." -ForegroundColor Yellow
    uv venv --python $PythonVersion .venv

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 虚拟环境创建失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 虚拟环境创建成功" -ForegroundColor Green

    # 激活虚拟环境
    Write-Host "🔌 激活虚拟环境..." -ForegroundColor Yellow
    & ".venv\Scripts\Activate.ps1"

    # 验证Python版本
    $pythonVersion = python --version
    Write-Host "✅ Python版本: $pythonVersion" -ForegroundColor Green

    # 升级pip和基础工具
    Write-Host "⬆️ 升级基础工具..." -ForegroundColor Yellow
    uv pip install --upgrade pip setuptools wheel

    # 安装项目依赖
    Write-Host "📦 安装项目依赖..." -ForegroundColor Yellow
    if (Test-Path "enhanced_search_mcp/requirements.txt") {
        uv pip install -r enhanced_search_mcp/requirements.txt
        Write-Host "✅ 项目依赖安装完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 未找到requirements.txt，手动安装核心依赖..." -ForegroundColor Yellow
        uv pip install fastmcp fastapi uvicorn aiohttp python-dotenv pydantic
    }

    # 验证关键包安装
    Write-Host "`n🔍 验证关键包安装..." -ForegroundColor Yellow
    $packages = @("fastmcp", "fastapi", "uvicorn", "aiohttp", "pydantic")
    foreach ($package in $packages) {
        try {
            $version = python -c "import $package; print($package.__version__)" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ $package : $version" -ForegroundColor Green
            } else {
                Write-Host "❌ $package : 未安装" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ $package : 检查失败" -ForegroundColor Red
        }
    }

    # 显示环境信息
    Write-Host "`n📋 环境信息:" -ForegroundColor Cyan
    Write-Host "Python路径: $(python -c 'import sys; print(sys.executable)')" -ForegroundColor Gray
    Write-Host "虚拟环境: $(Get-Location)\.venv" -ForegroundColor Gray
    Write-Host "包数量: $(uv pip list | Measure-Object -Line | Select-Object -ExpandProperty Lines)" -ForegroundColor Gray

    Write-Host "`n🎉 uv虚拟环境设置完成！" -ForegroundColor Green
    Write-Host "`n📝 使用说明:" -ForegroundColor Cyan
    Write-Host "1. 激活环境: .\.venv\Scripts\Activate.ps1" -ForegroundColor White
    Write-Host "2. 启动服务: python enhanced_search_mcp\server_direct.py" -ForegroundColor White
    Write-Host "3. 退出环境: deactivate" -ForegroundColor White

} catch {
    Write-Host "❌ 设置失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
