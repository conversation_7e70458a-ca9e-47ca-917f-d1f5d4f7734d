*~
*/*~
*/*/*~
*/*/*/*~
*/*/*/*/*~

# Git
.git
.gitignore

# CI
.codeclimate.yml
.travis.yml
.taskcluster.yml

# Byte-compiled / optimized / DLL files
__pycache__/
*/__pycache__/
*/*/__pycache__/
*/*/*/__pycache__/
*.py[cod]
*/*.py[cod]
*/*/*.py[cod]
*/*/*/*.py[cod]

# node_modules
node_modules/
*/node_modules/
*/*/node_modules/
*/*/*/node_modules/
*/*/*/*/node_modules/

.tx/

# to sync with .gitignore
geckodriver.log
.coverage
coverage/
cache/
build/
dist/
local/
gh-pages/
*.egg-info/

# 日志文件
*.log
logs/
enhanced_search_mcp/logs/

# 临时文件
*.tmp
*.temp
.cache/
.pytest_cache/

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# 文档和说明
*.md
docs/
*.rst

# 测试文件
tests/
test_*.py
*_test.py

# Windows相关脚本
*.ps1
check_wsl_environment.ps1
diagnose_searxng.ps1
install_dependencies.ps1
install_uv.ps1
setup_uv_env.ps1
start_mcp_server.ps1
test_mcp_server.ps1
verify_installation.ps1

# Shell脚本（除了Docker相关）
scripts/
start_mcp.sh
*.sh
!docker/*.sh

# 其他不需要的文件
Makefile
babel.cfg
package.json
pyrightconfig*.json
agent_zero_config.json
CHANGELOG.rst
CONTRIBUTING.md
LICENSE
PULL_REQUEST_TEMPLATE.md
SECURITY.md
AUTHORS.rst

# 容器相关（避免嵌套）
container/
utils/
client/
