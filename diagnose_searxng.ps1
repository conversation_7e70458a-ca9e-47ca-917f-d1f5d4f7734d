# SearXNG状态诊断脚本

param(
    [string]$SearXNGUrl = "http://localhost:8885"
)

Write-Host "🔍 SearXNG状态诊断" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

Write-Host "`n🌐 检查SearXNG服务: $SearXNGUrl" -ForegroundColor Blue

# 测试1: 检查SearXNG端口是否开放
Write-Host "`n1️⃣ 检查SearXNG端口..." -ForegroundColor Yellow

$uri = [System.Uri]$SearXNGUrl
$host = $uri.Host
$port = $uri.Port

try {
    $tcpTest = Test-NetConnection -ComputerName $host -Port $port -WarningAction SilentlyContinue
    if ($tcpTest.TcpTestSucceeded) {
        Write-Host "✅ SearXNG端口 $port 可访问" -ForegroundColor Green
    } else {
        Write-Host "❌ SearXNG端口 $port 不可访问" -ForegroundColor Red
        Write-Host "💡 SearXNG服务可能未启动" -ForegroundColor Yellow

        # 检查是否有进程在监听该端口
        $processes = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "   但是有进程在监听该端口:" -ForegroundColor Yellow
            foreach ($proc in $processes) {
                $processInfo = Get-Process -Id $proc.OwningProcess -ErrorAction SilentlyContinue
                if ($processInfo) {
                    Write-Host "   进程: $($processInfo.ProcessName) (PID: $($proc.OwningProcess))" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "   没有进程在监听端口 $port" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ 端口检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 检查SearXNG HTTP响应
Write-Host "`n2️⃣ 检查SearXNG HTTP响应..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri $SearXNGUrl -Method GET -TimeoutSec 10
    Write-Host "✅ SearXNG HTTP响应成功" -ForegroundColor Green
    Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "   内容长度: $($response.Content.Length) 字节" -ForegroundColor Gray

    # 检查是否是SearXNG页面
    if ($response.Content -match "SearXNG|searxng") {
        Write-Host "   ✅ 确认是SearXNG页面" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ 可能不是SearXNG页面" -ForegroundColor Yellow
    }

} catch {
    Write-Host "❌ SearXNG HTTP请求失败: $($_.Exception.Message)" -ForegroundColor Red

    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "   状态码: $statusCode" -ForegroundColor Red
    }
}

# 测试3: 检查SearXNG搜索API
Write-Host "`n3️⃣ 检查SearXNG搜索API..." -ForegroundColor Yellow

$searchUrl = "$SearXNGUrl/search"
$searchParams = @{
    q = "test"
    format = "json"
    engines = "duckduckgo"
}

try {
    $searchResponse = Invoke-RestMethod -Uri $searchUrl -Method GET -Body $searchParams -TimeoutSec 15
    Write-Host "✅ SearXNG搜索API响应成功" -ForegroundColor Green

    if ($searchResponse.results) {
        Write-Host "   搜索结果数量: $($searchResponse.results.Count)" -ForegroundColor Gray
        Write-Host "   查询: $($searchResponse.search.q)" -ForegroundColor Gray
        Write-Host "   引擎: $($searchResponse.search.engines -join ', ')" -ForegroundColor Gray
    } else {
        Write-Host "   ⚠️ 搜索响应格式异常" -ForegroundColor Yellow
    }

} catch {
    Write-Host "❌ SearXNG搜索API失败: $($_.Exception.Message)" -ForegroundColor Red

    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "   状态码: $statusCode" -ForegroundColor Red
    }
}

# 测试4: 检查Windows上的SearXNG安装
Write-Host "`n4️⃣ 检查Windows上的SearXNG安装..." -ForegroundColor Yellow

# 检查Python中是否可以导入searx模块
try {
    $pythonTest = python -c "import searx; print('SearXNG模块可用')" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python中可以导入SearXNG模块" -ForegroundColor Green
        Write-Host "   $pythonTest" -ForegroundColor Gray
    } else {
        Write-Host "❌ Python中无法导入SearXNG模块" -ForegroundColor Red
        Write-Host "   这是正常的，因为SearXNG主要为Linux设计" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Python测试失败" -ForegroundColor Red
}

# 检查是否有Docker容器运行SearXNG
Write-Host "`n5️⃣ 检查Docker中的SearXNG..." -ForegroundColor Yellow

try {
    $dockerContainers = docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Ports}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        $searxngContainers = $dockerContainers | Select-String -Pattern "searxng|searx"
        if ($searxngContainers) {
            Write-Host "✅ 发现SearXNG Docker容器:" -ForegroundColor Green
            $searxngContainers | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
        } else {
            Write-Host "⚠️ 未发现SearXNG Docker容器" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ Docker不可用或未安装" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Docker检查失败" -ForegroundColor Yellow
}

# 诊断结果和建议
Write-Host "`n📋 诊断结果:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan

Write-Host "`n💡 SearXNG在Windows上的解决方案:" -ForegroundColor Yellow
Write-Host "1. 使用Docker运行SearXNG (推荐)" -ForegroundColor White
Write-Host "2. 使用WSL2运行SearXNG" -ForegroundColor White
Write-Host "3. 连接到远程SearXNG实例" -ForegroundColor White
Write-Host "4. 使用备用搜索引擎API" -ForegroundColor White

Write-Host "`n🐳 Docker启动SearXNG命令:" -ForegroundColor Cyan
Write-Host @"
docker run -d \
  --name searxng \
  -p 8888:8080 \
  -v `${PWD}/searxng:/etc/searxng \
  -e SEARXNG_BASE_URL=http://localhost:8888/ \
  searxng/searxng:latest
"@ -ForegroundColor White

Write-Host "`n🔧 如果SearXNG不可用，建议:" -ForegroundColor Yellow
Write-Host "1. 修改 enhanced_search_engine.py 使用备用搜索API" -ForegroundColor Gray
Write-Host "2. 集成 DuckDuckGo API、Bing API 等" -ForegroundColor Gray
Write-Host "3. 使用 requests 直接调用搜索引擎" -ForegroundColor Gray
