version: '3.8'

services:
  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: searxng-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SearXNG + MCP服务器
  searxng-mcp:
    build:
      context: .
      dockerfile: Dockerfile.searxng-mcp
    container_name: searxng-mcp-server
    restart: unless-stopped
    ports:
      - "8885:8885"  # SearXNG Web界面
      - "8881:8881"  # MCP服务器
    volumes:
      - ./logs:/app/logs
      - ./enhanced_search_mcp/logs:/app/enhanced_search_mcp/logs
      - ./searx/settings.yml:/app/searx/settings.yml:ro
    environment:
      # SearXNG配置
      - SEARXNG_PORT=8885
      - SEARXNG_BIND_ADDRESS=0.0.0.0
      - SEARXNG_SETTINGS_PATH=/app/searx/settings.yml
      - SEARXNG_SECRET=your-secret-key-here
      - SEARXNG_BASE_URL=http://localhost:8885
      
      # MCP服务器配置
      - MCP_HOST=0.0.0.0
      - MCP_PORT=8881
      - SEARXNG_URL=http://localhost:8885
      - LOG_LEVEL=INFO
      
      # Redis配置（如果使用）
      - REDIS_URL=redis://redis:6379
      - ENABLE_CACHE=true
      
      # 搜索配置
      - MAX_CONCURRENT_SEARCHES=5
      - DEFAULT_ENGINES=google,bing,brave
      - MAX_RESULTS_PER_QUERY=10
      - MAX_TOTAL_RESULTS=20
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8885/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  redis_data:
    driver: local

networks:
  default:
    name: searxng-network
