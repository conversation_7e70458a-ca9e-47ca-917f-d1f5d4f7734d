# 安装uv包管理器 - Windows版本

Write-Host "🚀 安装uv包管理器..." -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

try {
    # 检查是否已安装uv
    $uvVersion = uv --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ uv已安装: $uvVersion" -ForegroundColor Green
        $update = Read-Host "是否要更新到最新版本? (y/N)"
        if ($update -eq 'y' -or $update -eq 'Y') {
            Write-Host "🔄 更新uv..." -ForegroundColor Yellow
            uv self update
        } else {
            Write-Host "✅ 使用现有版本" -ForegroundColor Green
            exit 0
        }
    } else {
        # 安装uv
        Write-Host "📦 下载并安装uv..." -ForegroundColor Yellow
        
        # 使用官方安装脚本
        Invoke-RestMethod https://astral.sh/uv/install.ps1 | Invoke-Expression
        
        # 刷新环境变量
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        
        # 验证安装
        $uvVersion = uv --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ uv安装成功: $uvVersion" -ForegroundColor Green
        } else {
            Write-Host "❌ uv安装失败，请手动安装" -ForegroundColor Red
            Write-Host "💡 访问: https://docs.astral.sh/uv/getting-started/installation/" -ForegroundColor Yellow
            exit 1
        }
    }
    
    # 显示uv信息
    Write-Host "`n📋 uv信息:" -ForegroundColor Cyan
    uv --version
    Write-Host "📍 uv位置: $(Get-Command uv).Source" -ForegroundColor Gray
    
    Write-Host "`n🎉 uv安装完成！" -ForegroundColor Green
    Write-Host "💡 现在可以使用uv创建虚拟环境了" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ 安装失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 请尝试手动安装: https://docs.astral.sh/uv/" -ForegroundColor Yellow
    exit 1
}
