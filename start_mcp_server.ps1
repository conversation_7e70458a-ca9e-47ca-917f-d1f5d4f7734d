# Enhanced Search MCP服务器启动脚本

param(
    [int]$Port = 8883,
    [string]$ServerHost = "0.0.0.0",
    [switch]$Dev = $false
)

Write-Host "🌐 启动Enhanced Search MCP服务器" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$ErrorActionPreference = "Stop"

try {
    # 检查虚拟环境
    Write-Host "`n1️⃣ 检查虚拟环境..." -ForegroundColor Yellow
    
    if (-not (Test-Path ".venv\Scripts\Activate.ps1")) {
        Write-Host "❌ 虚拟环境不存在" -ForegroundColor Red
        Write-Host "💡 请先运行: .\setup_uv_env.ps1" -ForegroundColor Yellow
        exit 1
    }

    # 激活虚拟环境
    Write-Host "🔌 激活uv虚拟环境..." -ForegroundColor Gray
    & ".venv\Scripts\Activate.ps1"

    # 验证Python环境
    $pythonPath = python -c "import sys; print(sys.executable)" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Python环境异常" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 虚拟环境已激活" -ForegroundColor Green
    Write-Host "   Python路径: $pythonPath" -ForegroundColor Gray

    # 检查关键依赖
    Write-Host "`n2️⃣ 检查关键依赖..." -ForegroundColor Yellow
    $dependencies = @("fastmcp", "fastapi", "uvicorn", "aiohttp")
    $allInstalled = $true

    foreach ($dep in $dependencies) {
        try {
            $version = python -c "import $dep; print($dep.__version__)" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "   ✅ $dep : $version" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $dep : 未安装" -ForegroundColor Red
                $allInstalled = $false
            }
        } catch {
            Write-Host "   ❌ $dep : 检查失败" -ForegroundColor Red
            $allInstalled = $false
        }
    }

    if (-not $allInstalled) {
        Write-Host "`n💡 部分依赖缺失，请运行: .\install_dependencies.ps1" -ForegroundColor Yellow
        exit 1
    }

    # 检查项目文件
    Write-Host "`n3️⃣ 检查项目文件..." -ForegroundColor Yellow
    
    if (-not (Test-Path "enhanced_search_mcp\server.py")) {
        Write-Host "❌ 服务器文件不存在" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 服务器文件存在" -ForegroundColor Green

    # 设置环境变量
    Write-Host "`n4️⃣ 设置环境变量..." -ForegroundColor Yellow
    
    $env:SEARXNG_URL = if ($env:SEARXNG_URL) { $env:SEARXNG_URL } else { "http://localhost:8888" }
    $env:MCP_HOST = $ServerHost
    $env:MCP_PORT = $Port.ToString()
    $env:LOG_LEVEL = if ($Dev) { "DEBUG" } else { "INFO" }
    
    Write-Host "   SearXNG URL: $env:SEARXNG_URL" -ForegroundColor Gray
    Write-Host "   MCP主机: $env:MCP_HOST" -ForegroundColor Gray
    Write-Host "   MCP端口: $env:MCP_PORT" -ForegroundColor Gray
    Write-Host "   日志级别: $env:LOG_LEVEL" -ForegroundColor Gray

    # 检查端口占用
    Write-Host "`n5️⃣ 检查端口占用..." -ForegroundColor Yellow
    
    $portInUse = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Host "⚠️ 端口 $Port 已被占用" -ForegroundColor Yellow
        $kill = Read-Host "是否终止占用进程? (y/N)"
        if ($kill -eq 'y' -or $kill -eq 'Y') {
            $portInUse | ForEach-Object { 
                try {
                    Stop-Process -Id $_.OwningProcess -Force
                    Write-Host "   ✅ 已终止进程 $($_.OwningProcess)" -ForegroundColor Green
                } catch {
                    Write-Host "   ⚠️ 无法终止进程 $($_.OwningProcess)" -ForegroundColor Yellow
                }
            }
            Start-Sleep -Seconds 2
        }
    } else {
        Write-Host "✅ 端口 $Port 可用" -ForegroundColor Green
    }

    # 显示启动信息
    Write-Host "`n🎯 服务器配置:" -ForegroundColor Cyan
    Write-Host "   版本: v2.0" -ForegroundColor White
    Write-Host "   主机: $ServerHost" -ForegroundColor White
    Write-Host "   端口: $Port" -ForegroundColor White
    Write-Host "   模式: $(if ($Dev) { '开发模式' } else { '生产模式' })" -ForegroundColor White
    Write-Host "   传输协议: Server-Sent Events (SSE)" -ForegroundColor White
    
    Write-Host "`n📡 服务端点:" -ForegroundColor Cyan
    Write-Host "   - SSE连接: http://localhost:$Port/sse" -ForegroundColor White
    Write-Host "   - 消息端点: http://localhost:$Port/messages/" -ForegroundColor White
    Write-Host "   - Docker访问: http://host.docker.internal:$Port/sse" -ForegroundColor White
    Write-Host "   - 网络访问: http://$(hostname):$Port/sse" -ForegroundColor White
    
    Write-Host "`n🌐 技术特色:" -ForegroundColor Magenta
    Write-Host "   ✅ 使用Server-Sent Events传输协议" -ForegroundColor Green
    Write-Host "   ✅ 兼容Agent-Zero等更多客户端" -ForegroundColor Green
    Write-Host "   ✅ 实时双向通信支持" -ForegroundColor Green
    Write-Host "   ✅ 更好的网络兼容性" -ForegroundColor Green
    
    Write-Host "`n🚀 正在启动服务器..." -ForegroundColor Yellow
    Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Gray
    Write-Host ""
    
    # 切换到MCP目录并启动
    Set-Location "enhanced_search_mcp"
    
    if ($Dev) {
        # 开发模式：使用uvicorn直接启动，支持热重载
        Write-Host "🛠️ 开发模式启动..." -ForegroundColor Yellow
        python -m uvicorn server:mcp.sse_app --host $ServerHost --port $Port --reload
    } else {
        # 生产模式：直接运行服务器
        Write-Host "🏭 生产模式启动..." -ForegroundColor Yellow
        python server.py
    }

} catch {
    Write-Host "`n❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`n🔧 故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 确保虚拟环境已创建: .\setup_uv_env.ps1" -ForegroundColor White
    Write-Host "2. 安装所有依赖: .\install_dependencies.ps1" -ForegroundColor White
    Write-Host "3. 验证安装: .\verify_installation.ps1" -ForegroundColor White
    Write-Host "4. 检查端口占用情况" -ForegroundColor White
    exit 1
} finally {
    # 返回原目录
    Set-Location $PSScriptRoot
    Write-Host "`n👋 服务器已停止" -ForegroundColor Yellow
}
