# 检查WSL环境和网络配置

Write-Host "🐧 检查WSL环境和网络配置" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# 检查WSL是否安装
Write-Host "`n1️⃣ 检查WSL状态..." -ForegroundColor Yellow
try {
    $wslVersion = wsl --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL已安装" -ForegroundColor Green
        Write-Host $wslVersion -ForegroundColor Gray
    } else {
        Write-Host "❌ WSL未安装或版本过旧" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ WSL检查失败" -ForegroundColor Red
    exit 1
}

# 检查WSL发行版
Write-Host "`n2️⃣ 检查WSL发行版..." -ForegroundColor Yellow
try {
    $wslList = wsl -l -v
    Write-Host "已安装的WSL发行版:" -ForegroundColor Gray
    $wslList | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
    
    # 检查默认发行版
    $defaultDistro = wsl -l -q | Select-Object -First 1
    Write-Host "默认发行版: $defaultDistro" -ForegroundColor Gray
} catch {
    Write-Host "❌ 无法获取WSL发行版信息" -ForegroundColor Red
}

# 获取WSL IP地址
Write-Host "`n3️⃣ 获取WSL IP地址..." -ForegroundColor Yellow
try {
    $wslIP = wsl hostname -I
    $wslIP = $wslIP.Trim()
    
    if ($wslIP) {
        Write-Host "✅ WSL IP地址: $wslIP" -ForegroundColor Green
        
        # 测试WSL网络连通性
        Write-Host "🔍 测试WSL网络连通性..." -ForegroundColor Gray
        $pingResult = Test-Connection -ComputerName $wslIP -Count 2 -Quiet
        if ($pingResult) {
            Write-Host "✅ Windows可以ping通WSL" -ForegroundColor Green
        } else {
            Write-Host "❌ Windows无法ping通WSL" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 无法获取WSL IP地址" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ WSL IP地址获取失败" -ForegroundColor Red
}

# 检查Windows IP地址
Write-Host "`n4️⃣ 检查Windows网络配置..." -ForegroundColor Yellow
try {
    # 获取Windows主机IP
    $windowsIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "以太网*" | Where-Object {$_.IPAddress -notlike "169.254.*"})[0].IPAddress
    if (-not $windowsIP) {
        $windowsIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi*" | Where-Object {$_.IPAddress -notlike "169.254.*"})[0].IPAddress
    }
    
    Write-Host "Windows主机IP: $windowsIP" -ForegroundColor Gray
    
    # 检查vEthernet (WSL)接口
    $wslInterface = Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "*WSL*" -ErrorAction SilentlyContinue
    if ($wslInterface) {
        Write-Host "WSL虚拟网卡IP: $($wslInterface.IPAddress)" -ForegroundColor Gray
    } else {
        Write-Host "⚠️ 未找到WSL虚拟网卡" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Windows网络配置检查失败" -ForegroundColor Yellow
}

# 检查防火墙状态
Write-Host "`n5️⃣ 检查Windows防火墙..." -ForegroundColor Yellow
try {
    $firewallProfiles = Get-NetFirewallProfile
    foreach ($profile in $firewallProfiles) {
        $status = if ($profile.Enabled) { "启用" } else { "禁用" }
        Write-Host "   $($profile.Name): $status" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ 防火墙状态检查失败" -ForegroundColor Yellow
}

# 检查项目目录在WSL中的映射
Write-Host "`n6️⃣ 检查项目目录映射..." -ForegroundColor Yellow
try {
    $currentPath = Get-Location
    $wslPath = $currentPath.Path -replace "^([A-Z]):", "/mnt/$($matches[1].ToLower())" -replace "\\", "/"
    Write-Host "Windows路径: $currentPath" -ForegroundColor Gray
    Write-Host "WSL路径: $wslPath" -ForegroundColor Gray
    
    # 测试WSL中是否可以访问项目目录
    $wslTest = wsl test -d $wslPath
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL可以访问项目目录" -ForegroundColor Green
    } else {
        Write-Host "❌ WSL无法访问项目目录" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ 目录映射检查失败" -ForegroundColor Yellow
}

Write-Host "`n📋 网络解决方案建议:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan
Write-Host "1. 使用WSL IP地址直接访问服务" -ForegroundColor White
Write-Host "2. 配置端口转发 (netsh portproxy)" -ForegroundColor White
Write-Host "3. 使用localhost端口映射" -ForegroundColor White
Write-Host "4. 配置Windows防火墙规则" -ForegroundColor White

if ($wslIP) {
    Write-Host "`n🔗 推荐配置:" -ForegroundColor Cyan
    Write-Host "WSL服务地址: http://$wslIP:8883/sse" -ForegroundColor White
    Write-Host "Windows访问地址: http://localhost:8883/sse (需要端口转发)" -ForegroundColor White
}
