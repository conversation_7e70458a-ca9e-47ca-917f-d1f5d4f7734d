#!/usr/bin/env python3
"""
测试SearXNG Python模块是否可以在Windows上直接使用
"""

import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_searxng_import():
    """测试SearXNG模块导入"""
    print("🔍 测试SearXNG模块导入...")
    
    try:
        # 尝试导入searx模块
        import searx
        print("✅ 成功导入 searx 模块")
        print(f"   searx 模块路径: {searx.__file__}")
        
        # 尝试导入搜索相关模块
        try:
            from searx.search import Search
            print("✅ 成功导入 searx.search.Search")
        except ImportError as e:
            print(f"❌ 无法导入 searx.search.Search: {e}")
        
        try:
            from searx.search.models import SearchQuery, EngineRef
            print("✅ 成功导入 searx.search.models")
        except ImportError as e:
            print(f"❌ 无法导入 searx.search.models: {e}")
        
        try:
            import searx.engines
            print("✅ 成功导入 searx.engines")
            print(f"   可用引擎数量: {len(searx.engines.engines) if hasattr(searx.engines, 'engines') else '未知'}")
        except ImportError as e:
            print(f"❌ 无法导入 searx.engines: {e}")
        
        try:
            import searx.settings
            print("✅ 成功导入 searx.settings")
        except ImportError as e:
            print(f"❌ 无法导入 searx.settings: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入 searx 模块: {e}")
        return False

def test_searxng_installation():
    """测试SearXNG是否已安装"""
    print("\n🔍 检查SearXNG安装状态...")
    
    try:
        import pkg_resources
        try:
            pkg_resources.get_distribution('searxng')
            print("✅ SearXNG 已通过pip安装")
            return True
        except pkg_resources.DistributionNotFound:
            print("❌ SearXNG 未通过pip安装")
    except ImportError:
        print("⚠️ pkg_resources 不可用")
    
    # 检查是否有searx目录在Python路径中
    for path in sys.path:
        searx_path = os.path.join(path, 'searx')
        if os.path.exists(searx_path):
            print(f"✅ 找到searx目录: {searx_path}")
            return True
    
    print("❌ 未找到searx模块")
    return False

def test_direct_search():
    """测试直接使用SearXNG进行搜索"""
    print("\n🔍 测试直接搜索功能...")
    
    try:
        # 导入必要模块
        from searx.search import Search
        from searx.search.models import SearchQuery, EngineRef
        import searx.engines
        import searx.settings
        
        print("✅ 所有必要模块导入成功")
        
        # 初始化设置
        searx.settings.load_settings()
        print("✅ SearXNG设置加载成功")
        
        # 创建搜索查询
        query = SearchQuery(
            query="test search",
            engineref_list=[EngineRef("duckduckgo", "general")],
            lang="zh-CN",
            safesearch=1,
            pageno=1
        )
        print("✅ 搜索查询创建成功")
        
        # 执行搜索
        search = Search(query)
        results = search.search()
        
        print(f"✅ 搜索执行成功，结果数量: {len(results.results)}")
        
        # 显示前几个结果
        for i, result in enumerate(results.results[:3]):
            print(f"   结果 {i+1}: {result.get('title', 'No title')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 SearXNG Python模块测试")
    print("=" * 40)
    
    # 显示Python环境信息
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 测试1: 检查安装状态
    installation_ok = test_searxng_installation()
    
    # 测试2: 测试模块导入
    import_ok = test_searxng_import()
    
    # 测试3: 测试直接搜索（仅在前两个测试通过时）
    search_ok = False
    if installation_ok and import_ok:
        search_ok = test_direct_search()
    
    # 总结
    print("\n📋 测试总结:")
    print("=" * 40)
    print(f"SearXNG安装: {'✅' if installation_ok else '❌'}")
    print(f"模块导入: {'✅' if import_ok else '❌'}")
    print(f"直接搜索: {'✅' if search_ok else '❌'}")
    
    if search_ok:
        print("\n🎉 SearXNG可以在Windows上直接使用Python模块！")
        print("建议使用 searxng_direct_adapter.py")
    else:
        print("\n⚠️ SearXNG无法在Windows上直接使用Python模块")
        print("建议使用以下替代方案:")
        print("1. 使用Docker运行SearXNG服务")
        print("2. 使用备用搜索适配器 (fallback_search_adapter.py)")
        print("3. 连接到远程SearXNG实例")

if __name__ == "__main__":
    main()
